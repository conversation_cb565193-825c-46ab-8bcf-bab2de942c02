/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreSettlement
{
    public partial class CompMarkSmallItem : UIBindT
    {
        public override string pkgName => "ExploreSettlement";
        public override string comName => "CompMarkSmallItem";

        public GLoader ldrTerrible;
        public GTextField tfText;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ldrTerrible = (GLoader)com.GetChildAt(0);
            tfText = (GTextField)com.GetChildAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ldrTerrible = null;
            tfText = null;
        }
    }
}