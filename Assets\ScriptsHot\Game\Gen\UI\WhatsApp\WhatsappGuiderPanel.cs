/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.WhatsApp
{
    public partial class WhatsappGuiderPanel : UIBindT
    {
        public override string pkgName => "WhatsApp";
        public override string comName => "WhatsappGuiderPanel";

        public GGraph imgBG;
        public GButton maskBtn;
        public GButton closeBtn;
        public GTextField content1;
        public GTextField content2;
        public GTextField content3;
        public GButton btn1;
        public GButton closeBtn2;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GGraph)com.GetChildAt(0);
            maskBtn = (GButton)com.GetChildAt(1);
            closeBtn = (GButton)com.GetChildAt(2);
            content1 = (GTextField)com.GetChildAt(6);
            content2 = (GTextField)com.GetChildAt(7);
            content3 = (GTextField)com.GetChildAt(9);
            btn1 = (GButton)com.GetChildAt(10);
            closeBtn2 = (GButton)com.GetChildAt(11);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            maskBtn = null;
            closeBtn = null;
            content1 = null;
            content2 = null;
            content3 = null;
            btn1 = null;
            closeBtn2 = null;
        }
    }
}