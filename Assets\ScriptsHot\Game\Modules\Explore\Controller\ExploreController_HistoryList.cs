﻿

using System;
using UnityEngine;
using Cysharp.Threading.Tasks;

public partial class ExploreController
{
    private const string GuideKey = "ExploreHistory";

    public void StartHistory()
    {
        if (HasShownInLast24Hours())
        {
            ShowGuide();
        }
    }

    private void ShowGuide()
    {
        CS_GetUserHistoryProgressListReq();
    }

    private bool HasShownInLast24Hours()
    {
        return true;
        if (!PlayerPrefs.HasKey(GuideKey)) return false;

        string saved = PlayerPrefs.GetString(GuideKey);
        if (DateTime.TryParse(saved, out DateTime lastShown))
        {
            return (DateTime.Now - lastShown).TotalHours < 24;
        }

        return false;
    }

    private void SaveGuideTriggerTime()
    {
        PlayerPrefs.SetString(GuideKey, DateTime.Now.ToString());
        PlayerPrefs.Save();
    }
}
