using System.Collections;
using System.Collections.Generic;
using UIBind.Debugger;
using UnityEngine;

namespace Game.Modules.Debugger
{
    /// <summary>
    /// DebuggerManager - Singleton manager for debugging functionality
    /// </summary>
    public class DebuggerManager
    {
        private static DebuggerManager _instance;
        
        public static DebuggerManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new DebuggerManager();
                }
                return _instance;
            }
        }

        public void Init()
        {
            ComboId.Bind();
        }
    }
}