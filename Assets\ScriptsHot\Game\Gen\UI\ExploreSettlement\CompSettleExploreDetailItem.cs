/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreSettlement
{
    public partial class CompSettleExploreDetailItem : UIBindT
    {
        public override string pkgName => "ExploreSettlement";
        public override string comName => "CompSettleExploreDetailItem";

        public GRichTextField tfTitle;
        public GRichTextField tfTitleDesc;
        public GTextField tfTime;
        public GTextField tfTimeDesc;
        public CompExploreProgress compProgress;
        public GTextField tfAppoint;
        public GTextField tfAppointDesc;
        public GGroup grp;
        public Transition rightShow;
        public Transition rightHide;
        public Transition show;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tfTitle = (GRichTextField)com.GetChildAt(2);
            tfTitleDesc = (GRichTextField)com.GetChildAt(3);
            tfTime = (GTextField)com.GetChildAt(6);
            tfTimeDesc = (GTextField)com.GetChildAt(7);
            compProgress = new CompExploreProgress();
            compProgress.Construct(com.GetChildAt(9).asCom);
            tfAppoint = (GTextField)com.GetChildAt(12);
            tfAppointDesc = (GTextField)com.GetChildAt(13);
            grp = (GGroup)com.GetChildAt(17);
            rightShow = com.GetTransitionAt(0);
            rightHide = com.GetTransitionAt(1);
            show = com.GetTransitionAt(2);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tfTitle = null;
            tfTitleDesc = null;
            tfTime = null;
            tfTimeDesc = null;
            compProgress.Dispose();
            compProgress = null;
            tfAppoint = null;
            tfAppointDesc = null;
            grp = null;
            rightShow = null;
            rightHide = null;
            show = null;
        }
    }
}