﻿

using System.Collections;
using System.Collections.Generic;
using FairyGUI;
using Msg.explore;
using UnityEngine;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// mission title
    /// </summary>
    public class ExploreProDescribeLogic:ItemComponentLogicBase
    {
        public ExploreProDescribe Com;
        
        private const int _proContainerWidth = 638;

        private List<ExploreStoryPro> _proItemList = new List<ExploreStoryPro>();
        
        private int _historyPer = 0;
        private Coroutine _barAnimCoroutine;
        public override void Init()
        {
            base.Init();
            _proItemList.Add(Com.pro1);
            _proItemList.Add(Com.pro2);
            _proItemList.Add(Com.pro3);
            _proItemList.Add(Com.pro4);
            _proItemList.Add(Com.pro5);
            Com.ctrl.selectedIndex = 0;
        }
        public void UpdateDescribe(PB_Story_Detail info,long entityId,bool ischange = false)
        {
            Com.ctrlDescribeBack.SetSelectedPage("back" + _controller.Model.GetDescribeBack(entityId));
            Com.txtTypeName.text = info.storyTitle;
            Com.txtTitle.text = info.storyProgress.currentTaskTitle;
            Com.txtDesc.text = info.storyProgress.currentStepDesc;
            Com.txtLevel.text = info.CefrLevel;
            
            // int32 finishedStepNum = 3; // 已完成step数量（必选，从0开始）
            // int32 stepTotalNum = 4; // step总数量（必选，task下有多少个step）
            
            StopBarAnimCoroutine();

            UpdateProLevel(info.storyProgress.finishedStepNo, info.storyProgress.stepTotal,ischange);
        }

        private void UpdateProLevel(int finishedStepNum, int stepTotalNum,bool ischange = false)
        {
            Debug.LogError($"finishedStepNum:::{finishedStepNum} stepTotalNum：：{stepTotalNum}");
            Com.ctrlProCount.selectedIndex = stepTotalNum - 1;
            for (int i = 0; i < _proItemList.Count; i++)
            {
                _proItemList[i].ctrlPerState.selectedPage = "close";
                _proItemList[i].ctrlBack.selectedPage = "black";
                _proItemList[i].ctrlProColor.selectedIndex = 0;
                _proItemList[i].txtNum.text = (i + 1).ToString();
                _proItemList[i].txtNumGray.text = (i + 1).ToString();
                _proItemList[i].backProValue.width = 0;
                _proItemList[i].com.visible = false;
            }

            for (int i = 0; i < stepTotalNum; i++)
            {
                _proItemList[i].com.visible = true;
                _proItemList[i].com.width = _proContainerWidth / stepTotalNum - 2;
            }
            for (int i = 0; i < finishedStepNum; i++)
            {
                _proItemList[i].ctrlPerState.selectedPage = "open";
                _proItemList[i].ctrlBack.selectedIndex = 0;
            }

            if (_proItemList.Count > finishedStepNum)
            {
                _proItemList[finishedStepNum].ctrlBack.selectedPage = "white";
            }
            
            // 只处理进度条动画部分
            StopBarAnimCoroutine();
            if (ischange)
            {
                _barAnimCoroutine = Timers.inst.StartCoroutine(AnimateAllBars(finishedStepNum));
            }
            else
            {
                for (int i = 0; i < finishedStepNum; i++)
                {
                    _proItemList[i].backProValue.width = _proItemList[i].backPro.width;
                }
                _historyPer = finishedStepNum;
            }
        }

        private IEnumerator AnimateAllBars(int finishedStepNum)
        {
            List<IEnumerator> anims = new List<IEnumerator>();
            for (int i = 0; i < finishedStepNum; i++)
            {
                var bar = _proItemList[i].backProValue;
                var targetWidth = _proItemList[i].backPro.width;

                if (i >= _historyPer)
                {
                    bar.width = 0;
                    SoundManger.instance.PlayUI("GetGold");
                    // 启动动画并等待其完成
                    yield return AnimateBarWidth(bar, targetWidth, 0.3f);
                }
                else
                {
                    bar.width = targetWidth;
                }
            }
            // 所有动画执行完毕后再赋值
            _historyPer = finishedStepNum;
        }

        private static IEnumerator AnimateBarWidth(GObject bar, float targetWidth, float duration)
        {
            float elapsed = 0f;
            float startWidth = 0f;
            bar.width = startWidth;
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                bar.width = Mathf.Lerp(startWidth, targetWidth, elapsed / duration);
                yield return null;
            }
            bar.width = targetWidth;
        }

        public void StopBarAnimCoroutine()
        {
            if (_barAnimCoroutine != null)
            {
                Timers.inst.StopCoroutine(_barAnimCoroutine);
                _barAnimCoroutine = null;
            }
        }
    }
}