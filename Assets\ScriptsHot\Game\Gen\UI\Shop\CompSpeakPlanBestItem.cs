/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class CompSpeakPlanBestItem : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "CompSpeakPlanBestItem";

        public Controller choice;
        public GTextField tfBest;
        public GTextField tfLifetime;
        public GTextField tfMoney;
        public GTextField tfPermanent;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            choice = com.GetControllerAt(0);
            tfBest = (GTextField)com.GetChildAt(3);
            tfLifetime = (GTextField)com.GetChildAt(4);
            tfMoney = (GTextField)com.GetChildAt(5);
            tfPermanent = (GTextField)com.GetChildAt(7);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            choice = null;
            tfBest = null;
            tfLifetime = null;
            tfMoney = null;
            tfPermanent = null;
        }
    }
}