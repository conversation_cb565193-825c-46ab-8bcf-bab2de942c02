# WhatsappGuiderUI 使用说明

## 概述

`WhatsappGuiderUI` 是一个基于项目 UI 框架的 WhatsApp 引导界面，继承自 `BaseUI<WhatsappGuiderPanel>`，提供了完整的点击事件处理和文本赋值功能。

## 文件结构

```
Assets/ScriptsHot/Game/Modules/WhatsApp/
├── WhatsappGuiderUI.cs          # 主要的 UI 类
├── WhatsAppManager.cs           # WhatsApp 功能管理器
├── WhatsAppUsageExample.cs      # 使用示例
└── WhatsappGuiderUI_README.md   # 本说明文档
```

## 主要功能

### 1. 基本 UI 功能
- ✅ 继承自 `BaseUI<WhatsappGuiderPanel>`
- ✅ 完整的生命周期管理（OnInit, OnShow, OnHide）
- ✅ 按钮点击事件处理
- ✅ 文本内容设置和国际化支持
- ✅ 全屏显示，位于 Top 层级

### 2. 交互功能
- **蒙版点击关闭**：点击背景蒙版关闭界面
- **关闭按钮**：两个关闭按钮都可以关闭界面
- **主要操作按钮**：执行 WhatsApp 相关操作

### 3. 文本管理
- **国际化支持**：自动设置国际化文本键
- **默认文本**：提供默认文本内容
- **自定义文本**：支持动态设置文本内容

### 4. WhatsApp 集成
- **安装检测**：自动检测 WhatsApp 是否已安装
- **智能跳转**：已安装则打开应用，未安装则引导下载
- **群聊功能**：支持打开 WhatsApp 群聊
- **分享功能**：支持分享应用到 WhatsApp

## 使用方法

### 1. 基本使用

```csharp
// 获取 UI 实例
var guiderUI = UIManager.instance.GetUI<WhatsappGuiderUI>(UIConsts.WhatsappGuider);

// 显示界面
guiderUI.Show();
```

### 2. 自定义文本内容

```csharp
// 设置自定义文本
guiderUI.SetTexts(
    "欢迎使用 WhatsApp 功能！",
    "您可以通过 WhatsApp 与朋友分享学习内容。",
    "点击下方按钮开始体验。",
    "开始使用"
);
```

### 3. 使用管理器（推荐）

```csharp
// 显示默认引导
WhatsAppManager.instance.ShowGuider(WhatsAppGuideType.Default);

// 显示群聊引导
WhatsAppManager.instance.ShowGuider(WhatsAppGuideType.GroupChat);

// 显示分享引导
WhatsAppManager.instance.ShowGuider(WhatsAppGuideType.Share);
```

### 4. 快速操作

```csharp
// 快速打开 WhatsApp
WhatsAppManager.instance.QuickOpenWhatsApp();

// 快速打开群聊
WhatsAppManager.instance.QuickOpenGroupChat();

// 快速分享
WhatsAppManager.instance.QuickShare("分享消息");
```

### 5. 静态工具类

```csharp
// 使用静态工具类
WhatsAppUIHelper.ShowWhatsAppGuider(WhatsAppGuideType.Default);
WhatsAppUIHelper.QuickOpenWhatsApp();
WhatsAppUIHelper.QuickShareToWhatsApp("Hello!");
```

## UI 组件说明

### WhatsappGuiderPanel 组件
- `imgBG`: 背景图
- `maskBtn`: 蒙版按钮（点击关闭）
- `closeBtn`: 关闭按钮
- `closeBtn_2`: 第二个关闭按钮
- `content1`: 第一段文本
- `content2`: 第二段文本
- `content3`: 第三段文本
- `btn1`: 主要操作按钮

## 引导类型

### WhatsAppGuideType 枚举
- `Default`: 默认引导（打开 WhatsApp）
- `GroupChat`: 群聊引导（加入群组）
- `Share`: 分享引导（分享应用）

## 国际化文本键

需要在国际化文件中添加以下文本键：

```
whatsapp_guider_content1: "欢迎使用 WhatsApp 功能！"
whatsapp_guider_content2: "您可以通过 WhatsApp 与朋友分享学习内容，或加入学习群组。"
whatsapp_guider_content3: "点击下方按钮开始体验 WhatsApp 功能。"
whatsapp_guider_btn1: "开始使用"
```

## 事件处理

### 按钮点击事件
- `OnMaskBtnClick()`: 蒙版点击，关闭界面
- `OnCloseBtnClick()`: 关闭按钮点击，关闭界面
- `OnBtn1Click()`: 主要操作按钮点击，执行 WhatsApp 操作
- `OnJoinGroupClick()`: 加入群组按钮点击（群聊模式）
- `OnShareClick()`: 分享按钮点击（分享模式）

## 错误处理

UI 包含完整的错误处理机制：
- Try-catch 包装所有 WhatsApp 操作
- 友好的错误提示
- 降级处理（未安装时引导下载）

## 扩展功能

### 1. 添加新的引导类型
```csharp
// 在 WhatsAppGuideType 枚举中添加新类型
public enum WhatsAppGuideType
{
    Default,
    GroupChat,
    Share,
    NewType  // 新增类型
}

// 在 WhatsAppManager 中添加处理逻辑
case WhatsAppGuideType.NewType:
    guiderUI.ShowNewTypeGuide();
    break;
```

### 2. 自定义按钮行为
```csharp
// 移除默认事件
RemoveUIEvent(ui.btn1.onClick, OnBtn1Click);

// 添加自定义事件
AddUIEvent(ui.btn1.onClick, CustomButtonClick);
```

## 注意事项

1. **UI 层级**：界面显示在 `UILayerConsts.Top` 层级
2. **全屏显示**：`isFullScreen = true`
3. **依赖关系**：需要 `WhatsAppHelper` 和相关 UI 组件
4. **平台兼容**：支持 Android 和 iOS 平台
5. **错误处理**：所有操作都有完整的错误处理

## 调试和测试

### GM 面板测试
可以在 GM 面板中添加以下测试按钮：
- 测试默认引导
- 测试群聊引导
- 测试分享引导
- 测试自定义内容

### 日志输出
所有操作都会输出详细的日志信息，便于调试：
```csharp
VFDebug.Log("WhatsappGuiderUI: 点击主要操作按钮");
```

## 更新日志

- v1.0: 基础 UI 功能实现
- v1.1: 添加 WhatsApp 集成功能
- v1.2: 添加管理器和工具类
- v1.3: 完善错误处理和用户体验
