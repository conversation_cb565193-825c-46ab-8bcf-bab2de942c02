#if UNITY_EDITOR

using System;
using UnityEngine;
using FairyGUI;
using UnityEngine.Rendering.Universal;
using ZTemp;


public class LoadComponent : MonoBehaviour
{
    public bool trigger = false;
    public bool trigger2 = false;
    public bool trigger3 = false;
    public bool trigger4 = false;
    public bool trigger5 = false;

    public string triggerName = "";
    public GameObject gameObject;

    private int tick = 0;
    
    private Material _material;

    public Camera camera;
    
    void TestFunction()
    {
        GObject obj = FindObjectByName(GRoot.inst, triggerName);
       // GObject obj = FindObjectByName(GRoot.inst,"BUTTON002");
        if (obj != null)
        {
           // ExtraUIPassManager.instance.AddRenderer(obj.displayObject);
           BeforeUIPassManager.AddRenderer(obj.displayObject);
        }
        else
        {
            Debug.LogError("没有找到叫这个名字的FGUI!");
        }
    }

    void TestFunction2()
    {
        GObject obj = FindObjectByName(GRoot.inst, triggerName);
        // GObject obj = FindObjectByName(GRoot.inst,"BUTTON002");
        if (obj != null)
        {
           // ExtraUIPassManager.instance.RemoveRenderer(obj.displayObject);
           BeforeUIPassManager.RemoveRenderer(obj.displayObject);
        }
        else
        {
            Debug.LogError("没有找到叫这个名字的FGUI!");
        }
    }

    void TestFunction3()
    {
            // 设置屏幕适配
        GRoot.inst.SetContentScaleFactor(750, 1624, UIContentScaler.ScreenMatchMode.MatchHeight);
    
        // 添加资源包
        UIPackage.AddPackage("PackageTest");
       // UIPackage.AddPackage("Shop");
    
        // 异步创建组件
        UIPackage.CreateObjectAsync("PackageTest", "Component1", OnCreateComponent);
        //    UIPackage.CreateObjectAsync("Shop", "BuyItemsPanel", OnCreateComponent);
        
        // if (gameObject != null)
        // {
        //     MeshRenderer meshRenderer = gameObject.GetComponent<MeshRenderer>();
        //     if (meshRenderer != null)
        //     {
        //         _material = meshRenderer.sharedMaterial;
        //        // meshRenderer.sharedMaterial.EnableKeyword("BLUR");
        //         Material mat = new Material(_material);
        //         mat.EnableKeyword("BLUR");
        //         meshRenderer.material=mat;
        //     }
        // }
    }
    
    void TestFunction4()
    {
        if (gameObject != null)
        {
            MeshRenderer meshRenderer = gameObject.GetComponent<MeshRenderer>();
            if (meshRenderer != null)
            {
                // meshRenderer.sharedMaterial.EnableKeyword("BLUR");
               // meshRenderer.material.DisableKeyword("BLUR");
               meshRenderer.material = _material;
            }
        }
    }
    
    void TestFunction5()
    {
        if (gameObject != null)
        {
            MeshRenderer meshRenderer = gameObject.GetComponent<MeshRenderer>();
            if (meshRenderer != null)
            {
                // meshRenderer.sharedMaterial.EnableKeyword("BLUR");
                meshRenderer.material = meshRenderer.sharedMaterial;
            }
        }
    }

    private void Update()
    {
        if (camera)
        {
            UniversalAdditionalCameraData additionalCameraData = camera.GetUniversalAdditionalCameraData();
            additionalCameraData.SetRenderer((int)RendererData.UI);
        }
        
       // ExtraUIPassManager.instance.Update();
        
        if (trigger)
        {
            TestFunction();
            trigger = false;
        }
        
        if (trigger2)
        {
            TestFunction2();
            trigger2 = false;
        }
        
        if (trigger3)
        {
            TestFunction3();
            trigger3 = false;
        }
        
        if (trigger4)
        {
            TestFunction4();
            trigger4 = false;
        }
        
        if (trigger5)
        {
            TestFunction5();
            trigger5 = false;
        }
    }

    // 回调函数，在组件创建完成后调用
    void OnCreateComponent(GObject gObject)
    {
        // 将 GObject 转换为 GComponent
        GComponent component = gObject.asCom;

        // 将组件添加到 GRoot
        GRoot.inst.AddChild(component);
        
        GObject obj = FindObjectByName(GRoot.inst, "watermelon1");
        if (obj != null)
        {
            ExtraUIPassManager.instance.AddRenderers(obj.displayObject);
        }
        
        GObject obj2 = FindObjectByName(GRoot.inst, "watermelon2");
        if (obj2 != null)
        {
            ExtraUIPassManager.instance.AddRenderers(obj2.displayObject);
        }
        
        GObject obj3 = FindObjectByName(GRoot.inst, "watermelon3");
        if (obj3 != null)
        {
            ExtraUIPassManager.instance.AddRenderers(obj3.displayObject);
        }
        
        GButton gButton = FindObjectByName(GRoot.inst, "CloseBtn2025") as GButton;
        if (gButton != null)
        {
            gButton.onClick.Add(() =>
            {
                gObject.Dispose();
            });
            ExtraUIPassManager.instance.AddRenderers(gButton.displayObject);
        }
        
    }
    
    public static GObject FindObjectByName(GObject parent, string name)
    {
        // 如果当前对象名称匹配，直接返回
        if (parent.name == name)
            return parent;

        // 如果是容器，递归搜索子对象
        if (parent is GComponent parentComponent)
        {
            for (int i = 0; i < parentComponent.numChildren; i++)
            {
                GObject child = parentComponent.GetChildAt(i);
                GObject result = FindObjectByName(child, name);
                if (result != null)
                    return result;
            }
        }

        // 未找到返回 null
        return null;
    }

}

#endif