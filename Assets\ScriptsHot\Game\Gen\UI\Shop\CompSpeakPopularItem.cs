/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class CompSpeakPopularItem : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "CompSpeakPopularItem";

        public Controller choice;
        public Controller state;
        public Controller discount;
        public GTextField tfTitle;
        public GTextField tfCur;
        public GTextField tfEquivalent;
        public GTextField tfMo;
        public GTextField tfMost;
        public GTextField discountTxt;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            choice = com.GetControllerAt(0);
            state = com.GetControllerAt(1);
            discount = com.GetControllerAt(2);
            tfTitle = (GTextField)com.GetChildAt(3);
            tfCur = (GTextField)com.GetChildAt(4);
            tfEquivalent = (GTextField)com.GetChildAt(5);
            tfMo = (GTextField)com.GetChildAt(6);
            tfMost = (GTextField)com.GetChildAt(9);
            discountTxt = (GTextField)com.GetChildAt(13);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            choice = null;
            state = null;
            discount = null;
            tfTitle = null;
            tfCur = null;
            tfEquivalent = null;
            tfMo = null;
            tfMost = null;
            discountTxt = null;
        }
    }
}