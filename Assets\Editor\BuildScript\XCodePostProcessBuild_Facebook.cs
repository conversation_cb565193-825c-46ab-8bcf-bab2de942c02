using System.Collections;
using System.Collections.Generic;
using System.IO;
using Tools.Build;
using UnityEditor;
using UnityEditor.Build.Reporting;
using UnityEditor.iOS.Xcode;
using UnityEngine;

public class XCodePostProcessBuild_Facebook
{
    public static void OnPostprocessBuild(BuildReport report)
    {
        BuildTarget target = report.summary.platform;
        string buildPath = report.summary.outputPath;


        if (target == BuildTarget.iOS)
        {
            Debug.Log("============================ XCodePostProcessBuild_Facebook");
            UpdateInfoPlist(buildPath);
            Debug.Log("============================ XCodePostProcessBuild_Facebook End");
        }
    }

    private static void UpdateInfoPlist(string path)
    {
        string plistPath = Path.Combine(path, "Info.plist");
        PlistDocument plist = new PlistDocument();
        plist.ReadFromFile(plistPath);

        // Facebook SDK 配置
        plist.root.SetString("FacebookAppID", "467028936039410");
        plist.root.SetString("FacebookDisplayName", "Talkit");
        plist.root.SetString("FacebookClientToken", "********************************");
        // Facebook URL Scheme
        PlistElementArray urlTypes;
        if (plist.root.values.ContainsKey("CFBundleURLTypes"))
        {
            urlTypes = plist.root["CFBundleURLTypes"].AsArray();
        }
        else
        {
            urlTypes = plist.root.CreateArray("CFBundleURLTypes");
        }
        PlistElementDict fbUrlDict = urlTypes.AddDict();
        fbUrlDict.SetString("CFBundleURLName", "facebook");
        PlistElementArray fbSchemes = fbUrlDict.CreateArray("CFBundleURLSchemes");
        fbSchemes.AddString("fb467028936039410");
        // Facebook AutoInit
        plist.root.SetBoolean("FacebookAutoLogAppEventsEnabled", true);
        plist.root.SetBoolean("FacebookAdvertiserIDCollectionEnabled", true);
        plist.root.SetBoolean("FacebookAutoInitEnabled", true);

        plist.WriteToFile(plistPath);
    }
}
