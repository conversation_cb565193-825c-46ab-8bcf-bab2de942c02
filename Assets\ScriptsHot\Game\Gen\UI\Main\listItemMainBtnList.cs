/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Main
{
    public partial class ListItemMainBtnList : GButton
    {
        public static string pkgName => "Main";
        public static string comName => "ListItemMainBtnList";
        public static string url => "ui://tnbxk55irie41y";

        public Controller type;
        public Controller markType;
        public GLoader3D book;
        public GLoader3D clipboard;
        public GLoader3D clock;
        public GLoader3D trophy;
        public GLoader3D house;
        public GLoader3D diy;
        public GLoader3D contacts;
        public GLoader3D fireNew;
        public GLoader3D survey;
        public GImage image_single;
        public GImage image_double;
        public GTextField text_single;
        public GTextField text_double;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ListItemMainBtnList));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            type = GetControllerAt(1);
            markType = GetControllerAt(2);
            book = GetChildAt(6) as GLoader3D;
            clipboard = GetChildAt(7) as GLoader3D;
            clock = GetChildAt(8) as GLoader3D;
            trophy = GetChildAt(9) as GLoader3D;
            house = GetChildAt(10) as GLoader3D;
            diy = GetChildAt(11) as GLoader3D;
            contacts = GetChildAt(12) as GLoader3D;
            fireNew = GetChildAt(13) as GLoader3D;
            survey = GetChildAt(14) as GLoader3D;
            image_single = GetChildAt(16) as GImage;
            image_double = GetChildAt(17) as GImage;
            text_single = GetChildAt(18) as GTextField;
            text_double = GetChildAt(19) as GTextField;
        }
        public override void Dispose()
        {
            type = null;
            markType = null;
            book = null;
            clipboard = null;
            clock = null;
            trophy = null;
            house = null;
            diy = null;
            contacts = null;
            fireNew = null;
            survey = null;
            image_single = null;
            image_double = null;
            text_single = null;
            text_double = null;

            base.Dispose();
        }
    }
}