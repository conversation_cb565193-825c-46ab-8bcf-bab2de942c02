/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class OnBoardPaywallPanel : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "OnBoardPaywallPanel";

        public GImage imgBG;
        public GButton btnBack;
        public CompOnboard CompContent;
        public GGraph btnGo;
        public GTextField tfGo;
        public GTextField tfSubscriptions;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GImage)com.GetChildAt(0);
            btnBack = (GButton)com.GetChildAt(2);
            CompContent = new CompOnboard();
            CompContent.Construct(com.GetChildAt(3).asCom);
            btnGo = (GGraph)com.GetChildAt(5);
            tfGo = (GTextField)com.GetChildAt(6);
            tfSubscriptions = (GTextField)com.GetChildAt(7);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            btnBack = null;
            CompContent.Dispose();
            CompContent = null;
            btnGo = null;
            tfGo = null;
            tfSubscriptions = null;
        }
    }
}