using System;
using UnityEngine;
using UnityEngine.Purchasing;
using CommonUI;
using UnityEngine.Purchasing.Extension;
using System.Text.RegularExpressions;
using LitJson;

namespace Game
{
    /// <summary>
    /// 支付失败处理器，用于处理各种支付失败情况并显示相应的弹窗
    /// 支持解析详细的Apple Store和Google Play错误代码
    /// </summary>
    public static class PaymentFailureHandler
    {
        /// <summary>
        /// 简化的错误类型枚举，匹配多语言文本
        /// </summary>
        public enum DetailedErrorType
        {
            NetworkError,                   // 网络错误 - ui_pay_error01, ui_pay_error08
            DeviceNotSupported,            // 设备不支持 - ui_pay_error02, ui_pay_error06
            PaymentNotAllowed,             // 支付不被允许 - ui_pay_error03
            RegionNotSupported,            // 地区不支持 - ui_pay_error05
            AccountIssue,                  // 账号问题 - ui_pay_error07
            UserCancelled,                 // 用户取消
            UnknownError                   // 未知错误
        }
        /// <summary>
        /// 处理支付失败并显示相应的弹窗
        /// </summary>
        /// <param name="failureReason">失败原因</param>
        /// <param name="failureMessage">失败消息</param>
        /// <param name="productId">商品ID</param>
        /// <param name="showError">是否显示错误弹窗</param>
        public static void HandlePaymentFailure(PurchaseFailureReason failureReason, string failureMessage, string productId, bool showError)
        {
            if (!showError)
            {
                VFDebug.Log($"支付失败但不显示错误弹窗: {failureReason} - {failureMessage}");
                return;
            }

            // 尝试解析详细错误类型
            DetailedErrorType detailedError = ParseDetailedErrorType(failureMessage);
            if (detailedError == DetailedErrorType.UnknownError)
            {
                detailedError = ParseDetailedErrorType(failureReason);
            }

            string title = GetFailureTitle(failureReason, detailedError);
            string content = GetFailureContent(failureReason, failureMessage, detailedError);
            string confirmButtonText = GetConfirmButtonText(failureReason, detailedError);
            int iconType = GetIconType(failureReason, detailedError);

            if (detailedError == DetailedErrorType.RegionNotSupported ||
                detailedError == DetailedErrorType.UnknownError)
            {
                UIManager.instance.GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToast(content, true);
            }
            else
            {
                ShowPaymentFailureDialog(title, content, confirmButtonText, iconType, failureReason, productId, detailedError);
            }
        }

        /// <summary>
        /// 处理支付失败（使用 PurchaseFailureDescription）
        /// </summary>
        /// <param name="failureDescription">失败描述</param>
        /// <param name="productId">商品ID</param>
        /// <param name="showError">是否显示错误弹窗</param>
        public static void HandlePaymentFailure(PurchaseFailureDescription failureDescription, string productId, bool showError)
        {
            HandlePaymentFailure(failureDescription.reason, failureDescription.message, productId, showError);
        }

        private static DetailedErrorType ParseDetailedErrorType(PurchaseFailureReason failureReason)
        {
            switch (failureReason)
            {
                case PurchaseFailureReason.UserCancelled:
                    return DetailedErrorType.UserCancelled;
                case PurchaseFailureReason.ProductUnavailable:
                    return DetailedErrorType.RegionNotSupported;
                case PurchaseFailureReason.PurchasingUnavailable:
                    return DetailedErrorType.DeviceNotSupported;
                case PurchaseFailureReason.ExistingPurchasePending:
                case PurchaseFailureReason.DuplicateTransaction:
                case PurchaseFailureReason.Unknown:
                    return DetailedErrorType.UnknownError;
            }
            
            return DetailedErrorType.UnknownError;
        }
        
        
        
        /// <summary>
        /// 解析详细的错误类型
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>详细错误类型</returns>
        private static DetailedErrorType ParseDetailedErrorType(string errorMessage)
        {
            if (string.IsNullOrEmpty(errorMessage))
                return DetailedErrorType.UnknownError;

            // 尝试解析JSON格式的错误消息
            try
            {
                if (errorMessage.StartsWith("{") && errorMessage.EndsWith("}"))
                {
                    var errorData = JsonMapper.ToObject(errorMessage);
                    if (errorData.ContainsKey("message"))
                    {
                        string message = errorData["message"].ToString();
                        return ParseErrorFromMessage(message);
                    }
                }
            }
            catch (Exception ex)
            {
                VFDebug.LogWarning($"解析JSON错误消息失败: {ex.Message}");
            }

            // 直接解析错误消息
            return ParseErrorFromMessage(errorMessage);
        }

        /// <summary>
        /// 从错误消息中解析错误类型
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>详细错误类型</returns>
        private static DetailedErrorType ParseErrorFromMessage(string message)
        {
            if (string.IsNullOrEmpty(message))
                return DetailedErrorType.UnknownError;

            string upperMessage = message.ToUpper();

            // 用户取消
            if (upperMessage.Contains("SKERRORPAYMENTCANCELLED") ||
                upperMessage.Contains("USER_CANCELED") ||
                upperMessage.Contains("USER CANCELED") ||
                upperMessage.Contains("CANCELLED") ||
                upperMessage.Contains("CANCELED"))
                return DetailedErrorType.UserCancelled;

            // 网络错误
            if (upperMessage.Contains("NETWORK") ||
                upperMessage.Contains("CONNECTION") ||
                upperMessage.Contains("INTERNET") ||
                upperMessage.Contains("OFFLINE") ||
                upperMessage.Contains("TIMEOUT"))
                return DetailedErrorType.NetworkError;

            // 设备不支持
            if (upperMessage.Contains("NOT SUPPORTED") ||
                upperMessage.Contains("UNSUPPORTED") ||
                upperMessage.Contains("BILLING_UNAVAILABLE") ||
                upperMessage.Contains("SKERRORCLIENTINVALID"))
                return DetailedErrorType.DeviceNotSupported;

            // 支付不被允许
            if (upperMessage.Contains("SKERRORPAYMENTNOTALLOWED") ||
                upperMessage.Contains("PAYMENT NOT ALLOWED") ||
                upperMessage.Contains("RESTRICTIONS") ||
                upperMessage.Contains("PARENTAL CONTROLS") ||
                upperMessage.Contains("SCREEN TIME"))
                return DetailedErrorType.PaymentNotAllowed;

            // 地区不支持
            if (upperMessage.Contains("REGION") ||
                upperMessage.Contains("COUNTRY") ||
                upperMessage.Contains("LOCATION") ||
                upperMessage.Contains("SKERRORSTOREPRODUCTNOTAVAILABLE") ||
                upperMessage.Contains("ITEM_UNAVAILABLE"))
                return DetailedErrorType.RegionNotSupported;

            // 账号问题
            if (upperMessage.Contains("ACCOUNT") ||
                upperMessage.Contains("SIGN IN") ||
                upperMessage.Contains("LOGIN") ||
                upperMessage.Contains("APPLE ID") ||
                upperMessage.Contains("GOOGLE ACCOUNT"))
                return DetailedErrorType.AccountIssue;

            // 辅助程序通信错误 - 归类为网络错误
            if (upperMessage.Contains("4097") ||
                upperMessage.Contains("HELPER") ||
                upperMessage.Contains("-1001") ||
                upperMessage.Contains("AUXILIAR"))
                return DetailedErrorType.NetworkError;

            return DetailedErrorType.UnknownError;
        }

        /// <summary>
        /// 获取失败标题 - 使用统一的标题
        /// </summary>
        private static string GetFailureTitle(PurchaseFailureReason reason, DetailedErrorType detailedError = DetailedErrorType.UnknownError)
        {
            return String.Empty;
        }

        /// <summary>
        /// 获取失败内容
        /// </summary>
        private static string GetFailureContent(PurchaseFailureReason reason, string originalMessage, DetailedErrorType detailedError = DetailedErrorType.UnknownError)
        {
            // 使用您提供的多语言键
            switch (detailedError)
            {
                case DetailedErrorType.NetworkError:
                    // 网络错误
                    return I18N.inst.MoStr("ui_pay_error01"); // "请检查您的网络连接后重试"

                case DetailedErrorType.DeviceNotSupported:
                    // 设备不支持
                    return I18N.inst.MoStr("ui_pay_error02"); // "当前设备暂不支持 App 内购买"

                case DetailedErrorType.PaymentNotAllowed:
                    // 支付不被允许
                    return I18N.inst.MoStr("ui_pay_error03"); // "请前往 设置 > 屏幕使用时间 > 内容与隐私访问限制 > iTunes Sotre 与 App Store 购买项目 中开启「App 内购买项目」。"

                case DetailedErrorType.RegionNotSupported:
                    // 地区不支持
                    return I18N.inst.MoStr("ui_pay_error05"); // "该订阅暂未在您所在地区上线"

                case DetailedErrorType.AccountIssue:
                    // 账号问题
                    return I18N.inst.MoStr("ui_pay_error07"); // "请检查设置或 Apple ID 登录状态"

                case DetailedErrorType.UserCancelled:
                    // 用户取消 - 不显示错误
                    return "";

                case DetailedErrorType.UnknownError:
                default:
                    // 未知错误，使用通用错误消息
                    return I18N.inst.MoStr("ui_pay_error08"); // "购买失败，请检查您的网络连接后再试"
            }
        }

        /// <summary>
        /// 获取确认按钮文本
        /// </summary>
        private static string GetConfirmButtonText(PurchaseFailureReason reason, DetailedErrorType detailedError = DetailedErrorType.UnknownError)
        {
            // 所有错误都使用统一的按钮文本
            return I18N.inst.MoStr("ui_pay_error04"); // "我知道了"
        }

        /// <summary>
        /// 获取图标类型
        /// </summary>
        private static int GetIconType(PurchaseFailureReason reason, DetailedErrorType detailedError = DetailedErrorType.UnknownError)
        {
            // 用户取消使用笑脸图标，其他错误使用委屈图标
            if (detailedError == DetailedErrorType.UserCancelled || reason == PurchaseFailureReason.UserCancelled)
                return 0; // 笑脸图标，表示用户主动取消

            return 1; // 委屈图标，表示错误
        }

        /// <summary>
        /// 显示支付失败对话框
        /// </summary>
        private static void ShowPaymentFailureDialog(string title, string content, string confirmButtonText, int iconType, PurchaseFailureReason reason, string productId, DetailedErrorType detailedError = DetailedErrorType.UnknownError)
        {
            VFDebug.Log($"显示支付失败弹窗: {title} - {content} (详细错误: {detailedError})");

            Action confirmAction = GetConfirmAction(reason, productId, detailedError);

            // 使用 CommConfirmUI 显示弹窗
            var confirmUI = UIManager.instance.GetUI<CommConfirmUI>(UIConsts.CommConfirm);
            confirmUI.Open(
                content: content,
                confirmFunc: confirmAction,
                cancelFunc: null,
                type: 1, // 单按钮
                confirmLabel: confirmButtonText,
                cancelLabel: "",
                block: false,
                iconType: iconType
            );

            VFDebug.Log($"显示支付失败弹窗: {reason} - {content}");
        }

        /// <summary>
        /// 获取确认按钮的行为
        /// </summary>
        private static Action GetConfirmAction(PurchaseFailureReason reason, string productId, DetailedErrorType detailedError = DetailedErrorType.UnknownError)
        {
            // 所有错误都使用统一的行为：关闭弹窗
            return () => {
                VFDebug.Log($"用户确认支付失败信息: {detailedError}");
                // 只是关闭弹窗，不执行其他操作
            };
        }
    }
}
