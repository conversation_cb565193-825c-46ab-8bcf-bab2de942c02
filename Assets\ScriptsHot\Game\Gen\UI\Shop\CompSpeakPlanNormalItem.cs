/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class CompSpeakPlanNormalItem : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "CompSpeakPlanNormalItem";

        public Controller choice;
        public GTextField tfTitle;
        public GTextField tfCur;
        public GTextField tfEquivalent;
        public GTextField tfMo;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            choice = com.GetControllerAt(0);
            tfTitle = (GTextField)com.GetChildAt(3);
            tfCur = (GTextField)com.GetChildAt(4);
            tfEquivalent = (GTextField)com.GetChildAt(5);
            tfMo = (GTextField)com.GetChildAt(6);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            choice = null;
            tfTitle = null;
            tfCur = null;
            tfEquivalent = null;
            tfMo = null;
        }
    }
}