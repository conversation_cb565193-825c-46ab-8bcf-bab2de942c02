﻿

using System.Collections.Generic;
using FairyGUI;
using Google.Protobuf.Collections;
using UIBind.Explore;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Explore
{
    public class ExploreMemoryPanelUI : BaseUI<ExploreMemoryPanel>
    {
        public override string uiLayer => UILayerConsts.Top;
        protected override bool isFullScreen => true;
        
        private List<ExploreMemoryItem> _memoryItemList = new List<ExploreMemoryItem>();
        
        private ExploreController _controller;

        public ExploreMemoryPanelUI(string name) : base(name)
        {
        }
        
        protected override void OnInit(GComponent uiCom)
        {
            _controller = GetController<ExploreController>(ModelConsts.Explore);
            ui.btnClose.onClick.Add(() =>
            {
                RepeatedField<global::Msg.explore.PB_StoryPreloadData> storyPreloadDataList = _controller.Model.HistoryProgressListResp.storyPreloadDataList;
                CLICK_EXPLORE_MISSION_STORY_GUIDE_POPUP_EXIT_BUTTON dt = new CLICK_EXPLORE_MISSION_STORY_GUIDE_POPUP_EXIT_BUTTON();
                dt.story_num = storyPreloadDataList.Count;
                if(storyPreloadDataList.Count >= 1)
                    dt.task_id_1 = storyPreloadDataList[0].taskId;
                if(storyPreloadDataList.Count >= 2)
                    dt.task_id_2 = storyPreloadDataList[1].taskId;
                if(storyPreloadDataList.Count >= 3)
                    dt.task_id_3 = storyPreloadDataList[2].taskId;
                DataDotMgr.Collect(dt);
                this.Hide();
            });
            
            ui.item1.InitItem();
            ui.item2.InitItem();
            ui.item3.InitItem();
            
            _memoryItemList.Clear();
            _memoryItemList.Add(ui.item1);
            _memoryItemList.Add(ui.item2);
            _memoryItemList.Add(ui.item3);

            ui.txtTitle.SetKey("ui_explore_history_guide_title_text");
        }
        
        protected override void OnShow()
        {
            UpdateInfo();
            
            HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
            if (!hCon.IsExploreTab())
            {
                Hide();
            }
        }

        private void UpdateInfo()
        {
            RepeatedField<global::Msg.explore.PB_StoryPreloadData> storyPreloadDataList = _controller.Model.HistoryProgressListResp.storyPreloadDataList;
            this.ui.ctrl.selectedIndex = storyPreloadDataList.Count > 0?storyPreloadDataList.Count -1 : 0;
            
            for (int i = 0; i < storyPreloadDataList.Count; i++)
            {
                if (_memoryItemList.Count > i)
                {
                    _memoryItemList[i].SetData(storyPreloadDataList[i]);
                }
            }
            
            APPEAR_EXPLORE_MISSION_STORY_GUIDE_POPUP dt = new APPEAR_EXPLORE_MISSION_STORY_GUIDE_POPUP();
            dt.story_num = storyPreloadDataList.Count;
            if(storyPreloadDataList.Count >= 1)
                 dt.task_id_1 = storyPreloadDataList[0].taskId;
            if(storyPreloadDataList.Count >= 2)
                dt.task_id_2 = storyPreloadDataList[1].taskId;
            if(storyPreloadDataList.Count >= 3)
                dt.task_id_3 = storyPreloadDataList[2].taskId;
            DataDotMgr.Collect(dt);
        }
    }
}