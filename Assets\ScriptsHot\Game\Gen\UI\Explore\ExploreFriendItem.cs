/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreFriendItem : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreFriendItem";

        public Controller ctrlTitleType;
        public GImage imgBG;
        public GLoader comBgImg;
        public GLoader comLoader3D;
        public GLoader comLoaderImg;
        public ExploreTitle comExploreHead;
        public ExploreDescribe comDescribe;
        public ExploreTarget comTarget;
        public ExploreProDescribe comMissionTitle;
        public GComponent comLoading;
        public ExploreAvatarItem comAvatarItem;
        public ExplorepPlayerItem comPlayer;
        public ExploreAdviceItem comAdcive;
        public ExploreScaffold comSaffold;
        public ExploreMissionAavtarTitle comFriendInfo;
        public GGraph imgMask;
        public ExploreContinueBtn comContinueBtn;
        public ExploreAwardPanel comAwardList;
        public GLoader3D bubbleSpine;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlTitleType = com.GetControllerAt(0);
            imgBG = (GImage)com.GetChildAt(0);
            comBgImg = (GLoader)com.GetChildAt(1);
            comLoader3D = (GLoader)com.GetChildAt(2);
            comLoaderImg = (GLoader)com.GetChildAt(3);
            comExploreHead = new ExploreTitle();
            comExploreHead.Construct(com.GetChildAt(4).asCom);
            comDescribe = new ExploreDescribe();
            comDescribe.Construct(com.GetChildAt(5).asCom);
            comTarget = new ExploreTarget();
            comTarget.Construct(com.GetChildAt(6).asCom);
            comMissionTitle = new ExploreProDescribe();
            comMissionTitle.Construct(com.GetChildAt(7).asCom);
            comLoading = (GComponent)com.GetChildAt(8);
            comAvatarItem = new ExploreAvatarItem();
            comAvatarItem.Construct(com.GetChildAt(9).asCom);
            comPlayer = new ExplorepPlayerItem();
            comPlayer.Construct(com.GetChildAt(10).asCom);
            comAdcive = new ExploreAdviceItem();
            comAdcive.Construct(com.GetChildAt(11).asCom);
            comSaffold = new ExploreScaffold();
            comSaffold.Construct(com.GetChildAt(12).asCom);
            comFriendInfo = new ExploreMissionAavtarTitle();
            comFriendInfo.Construct(com.GetChildAt(13).asCom);
            imgMask = (GGraph)com.GetChildAt(14);
            comContinueBtn = new ExploreContinueBtn();
            comContinueBtn.Construct(com.GetChildAt(15).asCom);
            comAwardList = new ExploreAwardPanel();
            comAwardList.Construct(com.GetChildAt(16).asCom);
            bubbleSpine = (GLoader3D)com.GetChildAt(17);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlTitleType = null;
            imgBG = null;
            comBgImg = null;
            comLoader3D = null;
            comLoaderImg = null;
            comExploreHead.Dispose();
            comExploreHead = null;
            comDescribe.Dispose();
            comDescribe = null;
            comTarget.Dispose();
            comTarget = null;
            comMissionTitle.Dispose();
            comMissionTitle = null;
            comLoading = null;
            comAvatarItem.Dispose();
            comAvatarItem = null;
            comPlayer.Dispose();
            comPlayer = null;
            comAdcive.Dispose();
            comAdcive = null;
            comSaffold.Dispose();
            comSaffold = null;
            comFriendInfo.Dispose();
            comFriendInfo = null;
            imgMask = null;
            comContinueBtn.Dispose();
            comContinueBtn = null;
            comAwardList.Dispose();
            comAwardList = null;
            bubbleSpine = null;
        }
    }
}