using System.Collections;
using System.Collections.Generic;
using Game.Modules.WhatsApp;
using Msg.incentive;
using UnityEngine;

public class WhatsappController : BaseController
{
    public WhatsappController() : base(ModelConsts.Whatsapp) { }
    
    public override void OnUIInit()
    {
        RegisterUI(new WhatsappGuiderUI(UIConsts.WhatsappGuider));
    }


    private PB_WhatsappInfo whatsappInfo;
    
    
    public bool ShowMainWhatsappBtn
    {
        get
        {
            if (whatsappInfo == null)
            {
                return false;
            }
            return whatsappInfo.is_show_whatsapp;
        }
    }
    
    public string WhatsappUrl
    {
        get
        {
            if (whatsappInfo == null)
            {
                return string.Empty;
            }
            return whatsappInfo.whatsapp_url;
        }
    }
    
    
    public void SetDatas(PB_WhatsappInfo _whatsappInfo)
    {
        whatsappInfo = _whatsappInfo;

        if (whatsappInfo.is_show_whatsapp)
        {
            SendBI_appear_incentive_whatsapp_icon();
        }


    }

    #region bi

    private void SendBI_appear_incentive_whatsapp_icon()
    {
        Dictionary<string, object> dic = BI.ApplyBiDic();
        BI.Collect("appear_incentive_whatsapp_icon" , dic);
    }
    public void SendBI_click_incentive_whatsapp_icon()
    {
        Dictionary<string, object> dic = BI.ApplyBiDic();
        BI.Collect("click_incentive_whatsapp_icon" , dic);
    }
    public void SendBI_appear_incentive_whatsapp_join_popup()
    {
        Dictionary<string, object> dic = BI.ApplyBiDic();
        BI.Collect("appear_incentive_whatsapp_join_popup" , dic);
    }
    public void SendBI_click_incentive_whatsapp_join_popup_reminder_button()
    {
        Dictionary<string, object> dic = BI.ApplyBiDic();
        BI.Collect("click_incentive_whatsapp_join_popup_reminder_button" , dic);
    }
    public void SendBI_appear_setting_page()
    {
        Dictionary<string, object> dic = BI.ApplyBiDic();
        BI.Collect("appear_setting_page" , dic);
    }
    public void SendBI_click_setting_page_whatsapp_button()
    {
        Dictionary<string, object> dic = BI.ApplyBiDic();
        BI.Collect("click_setting_page_whatsapp_button" , dic);
    }
    #endregion
}
