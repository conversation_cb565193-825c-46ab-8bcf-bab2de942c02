﻿using System.Collections.Generic;
using Msg.explore;

namespace ScriptsHot.Game.Modules.ExploreSettlement
{
    public class ExploreSettlementModel : BaseModel
    {
        public ExploreSettlementModel() : base(ModelConsts.ExploreSettlement)
        {
        }
        
        public enum ExploreSettle
        {
            GapItemHead,
            DetailItem,
            DayItem,
            MarkItem,
            NextItem,
            GapItemTail,
        }
        
        public SC_MissionStoryChatDownMsgForSettlement SettlementInfo { get; private set; }

        public Dictionary<ExploreSettle, string> SettleShowListUrl = new()
        {
            { ExploreSettle.GapItemHead, "ui://ExploreSettlement/CompSettleExploreGapHead" },
            { ExploreSettle.DetailItem, "ui://ExploreSettlement/CompSettleExploreDetailItem" },
            { ExploreSettle.DayItem, "ui://ExploreSettlement/CompSettleExploreDayItem" },
            { ExploreSettle.MarkItem, "ui://ExploreSettlement/CompSettleExploreMarkItem" },
            { ExploreSettle.NextItem, "ui://ExploreSettlement/CompSettleExploreNextItem" },
            { ExploreSettle.GapItemTail, "ui://ExploreSettlement/CompSettleExploreGapTail" },
        };

        public void SetSettlementInfo(SC_MissionStoryChatDownMsgForSettlement data)
        {
            SettlementInfo = data;
        }
    }
}