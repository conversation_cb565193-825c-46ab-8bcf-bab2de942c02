#if UNITY_EDITOR
using System.Collections;
using UnityEngine;
using UnityEngine.Networking;
using System.Text;
using Newtonsoft.Json;
using UnityEditor;

namespace ZTest.ZTemp
{
    /// <summary>
    /// GPT Editor调用工具 - 支持Azure OpenAI
    /// 在Editor环境下提供GPT API调用界面
    /// </summary>
    public class GPTEditorTool : MonoBehaviour
    {
        [Header("Azure OpenAI 配置")]
        [SerializeField] private string azureEndpoint = "https://japan-east-gpt-01.openai.azure.com/";
        [SerializeField] private string deploymentName = "gpt-4o-global-01";
        [SerializeField] private string apiKey = "********************************";
        [SerializeField] private string apiVersion = "2025-01-01-preview";
        
        [Header("模型参数")]
        [SerializeField] private float temperature = 0.7f;
        [SerializeField] private int maxTokens = 1000;
        [SerializeField] private float topP = 0.95f;
        [SerializeField] private float frequencyPenalty = 0f;
        [SerializeField] private float presencePenalty = 0f;
        
        [Header("系统提示")]
        [SerializeField] [TextArea(3, 5)] 
        private string systemPrompt = "You are an AI assistant that helps people find information.";
        
        [Header("用户输入")]
        [SerializeField] [TextArea(5, 10)] 
        private string userPrompt = "请介绍一下Unity游戏引擎的特点。";
        
        [Header("API响应")]
        [SerializeField] [TextArea(10, 20)] 
        private string apiResponse = "点击\"发送到Azure GPT\"按钮后，响应将显示在这里...";
        
        [Header("状态信息")]
        [SerializeField] private bool isProcessing = false;
        [SerializeField] private string lastError = "";
        [SerializeField] private float lastRequestTime = 0f;
        [SerializeField] private string performanceStats = "";
        
        /// <summary>
        /// 发送请求到Azure OpenAI API
        /// </summary>
        public void SendToGPT()
        {
            if (isProcessing)
            {
                Debug.LogWarning("正在处理中，请等待上一个请求完成...");
                return;
            }
            
            if (string.IsNullOrEmpty(userPrompt.Trim()))
            {
                Debug.LogError("用户输入不能为空！");
                lastError = "用户输入不能为空！";
                return;
            }
            
            if (string.IsNullOrEmpty(apiKey) || apiKey == "REPLACE_WITH_YOUR_KEY_VALUE_HERE")
            {
                Debug.LogError("请先配置Azure OpenAI API Key！");
                lastError = "请先配置Azure OpenAI API Key！";
                return;
            }
            
            StartCoroutine(SendToAzureGPTCoroutine(userPrompt));
        }
        
        /// <summary>
        /// Azure OpenAI API调用协程
        /// </summary>
        private IEnumerator SendToAzureGPTCoroutine(string prompt)
        {
            isProcessing = true;
            lastError = "";
            apiResponse = "正在发送请求到Azure GPT...";
            
            // 开始计时
            float startTime = Time.realtimeSinceStartup;
            System.DateTime requestStartTime = System.DateTime.Now;
            
            Debug.Log($"发送到Azure GPT: {prompt}");
            
            // 构建Azure OpenAI API URL
            string url = BuildAzureAPIUrl();
            Debug.Log($"API URL: {url}");
            
            // 构建请求数据
            var requestData = new AzureOpenAIRequest
            {
                messages = new AzureMessage[]
                {
                    new AzureMessage { role = "system", content = new AzureContent[] { new AzureContent { type = "text", text = systemPrompt } } },
                    new AzureMessage { role = "user", content = new AzureContent[] { new AzureContent { type = "text", text = prompt } } }
                },
                max_tokens = maxTokens,
                temperature = temperature,
                top_p = topP,
                frequency_penalty = frequencyPenalty,
                presence_penalty = presencePenalty,
                stop = null,
                stream = false
            };
            
            string jsonBody;
            try
            {
                jsonBody = JsonConvert.SerializeObject(requestData, new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore,
                    Formatting = Formatting.Indented
                });
                Debug.Log($"请求JSON: {jsonBody}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"JSON序列化失败：{ex.Message}");
                lastError = $"JSON序列化失败：{ex.Message}";
                apiResponse = $"错误：{lastError}";
                isProcessing = false;
                yield break;
            }
            
            using (UnityWebRequest request = new UnityWebRequest(url, "POST"))
            {
                byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonBody);
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("api-key", apiKey);  // Azure使用api-key而不是Authorization
                
                // 设置超时时间（60秒，Azure可能响应较慢）
                request.timeout = 60;
                
                yield return request.SendWebRequest();
                
                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        string responseText = request.downloadHandler.text;
                        Debug.Log("Azure GPT原始响应:\n" + responseText);
                        
                        // 解析响应
                        var response = JsonConvert.DeserializeObject<AzureGPTResponse>(responseText);
                        
                        if (response?.choices != null && response.choices.Length > 0 && 
                            response.choices[0].message?.content != null)
                        {
                            string content = response.choices[0].message.content;
                            apiResponse = $"Azure GPT响应：\n\n{content}";
                            Debug.Log($"Azure GPT响应内容：\n{content}");
                            
                            // 显示使用统计
                            if (response.usage != null)
                            {
                                apiResponse += $"\n\n📊 使用统计：\n";
                                apiResponse += $"• 提示Token: {response.usage.prompt_tokens}\n";
                                apiResponse += $"• 完成Token: {response.usage.completion_tokens}\n";
                                apiResponse += $"• 总Token: {response.usage.total_tokens}";
                            }
                        }
                        else
                        {
                            apiResponse = "响应格式异常：未找到有效内容";
                            lastError = "响应格式异常";
                        }
                    }
                    catch (System.Exception ex)
                    {
                        Debug.LogError($"解析响应失败：{ex.Message}");
                        lastError = $"解析响应失败：{ex.Message}";
                        apiResponse = $"解析错误：{lastError}\n\n原始响应：\n{request.downloadHandler.text}";
                    }
                }
                else
                {
                    string errorMsg = $"请求失败 [{request.responseCode}]：{request.error}";
                    if (!string.IsNullOrEmpty(request.downloadHandler.text))
                    {
                        errorMsg += $"\n响应内容：{request.downloadHandler.text}";
                    }
                    
                    Debug.LogError(errorMsg);
                    lastError = request.error;
                    apiResponse = errorMsg;
                    
                    // 针对常见错误给出建议
                    if (request.responseCode == 401)
                    {
                        apiResponse += "\n\n💡 建议：请检查API Key是否正确";
                    }
                    else if (request.responseCode == 404)
                    {
                        apiResponse += "\n\n💡 建议：请检查Endpoint和Deployment名称是否正确";
                    }
                    else if (request.responseCode == 429)
                    {
                        apiResponse += "\n\n💡 建议：请求过于频繁，请稍后重试";
                    }
                }
            }
            
            // 计算耗时
            float endTime = Time.realtimeSinceStartup;
            lastRequestTime = endTime - startTime;
            System.DateTime requestEndTime = System.DateTime.Now;
            
            // 生成性能统计信息
            performanceStats = $"⏱️ 请求耗时统计：\n";
            performanceStats += $"• 总耗时: {lastRequestTime:F2} 秒\n";
            performanceStats += $"• 开始时间: {requestStartTime:HH:mm:ss.fff}\n";
            performanceStats += $"• 结束时间: {requestEndTime:HH:mm:ss.fff}\n";
            
            // 性能评级
            string performanceRating;
            if (lastRequestTime < 2.0f)
                performanceRating = "🚀 极快";
            else if (lastRequestTime < 5.0f)
                performanceRating = "⚡ 快速";
            else if (lastRequestTime < 10.0f)
                performanceRating = "🐌 较慢";
            else
                performanceRating = "🐌 很慢";
                
            performanceStats += $"• 性能评级: {performanceRating}\n";
            
            // 将性能统计添加到响应中
            if (!string.IsNullOrEmpty(apiResponse) && !apiResponse.Contains("错误") && !apiResponse.Contains("失败"))
            {
                apiResponse += $"\n\n{performanceStats}";
            }
            
            Debug.Log($"Azure GPT请求完成，耗时: {lastRequestTime:F2}秒");
            
            isProcessing = false;
        }
        
        /// <summary>
        /// 构建Azure OpenAI API URL
        /// </summary>
        private string BuildAzureAPIUrl()
        {
            // 确保endpoint以/结尾
            string endpoint = azureEndpoint.TrimEnd('/');
            
            // 构建完整的API URL
            return $"{endpoint}/openai/deployments/{deploymentName}/chat/completions?api-version={apiVersion}";
        }
        
        /// <summary>
        /// 清空响应内容
        /// </summary>
        public void ClearResponse()
        {
            apiResponse = "响应已清空...";
            lastError = "";
            lastRequestTime = 0f;
            performanceStats = "";
        }
        
        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public void ResetToDefaults()
        {
            azureEndpoint = "https://japan-east-gpt-01.openai.azure.com/";
            deploymentName = "gpt-4o-global-01";
            apiVersion = "2025-01-01-preview";
            systemPrompt = "You are an AI assistant that helps people find information.";
            userPrompt = "请介绍一下Unity游戏引擎的特点。";
            apiResponse = "点击\"发送到Azure GPT\"按钮后，响应将显示在这里...";
            temperature = 0.7f;
            maxTokens = 800;
            topP = 0.95f;
            frequencyPenalty = 0f;
            presencePenalty = 0f;
            lastError = "";
            lastRequestTime = 0f;
            performanceStats = "";
        }
        
        /// <summary>
        /// 测试连接
        /// </summary>
        public void TestConnection()
        {
            string url = BuildAzureAPIUrl();
            Debug.Log($"将要连接的URL: {url}");
            apiResponse = $"测试连接信息：\n\nEndpoint: {azureEndpoint}\nDeployment: {deploymentName}\nAPI Version: {apiVersion}\n\n完整URL: {url}";
        }
    }
    
    /// <summary>
    /// Azure OpenAI请求数据结构
    /// </summary>
    [System.Serializable]
    public class AzureOpenAIRequest
    {
        public AzureMessage[] messages;
        public int max_tokens;
        public float temperature;
        public float top_p;
        public float frequency_penalty;
        public float presence_penalty;
        public string stop;
        public bool stream;
    }
    
    /// <summary>
    /// Azure消息结构
    /// </summary>
    [System.Serializable]
    public class AzureMessage
    {
        public string role;
        public AzureContent[] content;
    }
    
    /// <summary>
    /// Azure内容结构
    /// </summary>
    [System.Serializable]
    public class AzureContent
    {
        public string type;
        public string text;
    }
    
    /// <summary>
    /// Azure GPT API响应数据结构
    /// </summary>
    [System.Serializable]
    public class AzureGPTResponse
    {
        public string id;
        public string @object;
        public long created;
        public string model;
        public AzureChoice[] choices;
        public AzureUsage usage;
    }
    
    [System.Serializable]
    public class AzureChoice
    {
        public int index;
        public AzureResponseMessage message;
        public string finish_reason;
    }
    
    [System.Serializable]
    public class AzureResponseMessage
    {
        public string role;
        public string content;
    }
    
    [System.Serializable]
    public class AzureUsage
    {
        public int prompt_tokens;
        public int completion_tokens;
        public int total_tokens;
    }
    
    /// <summary>
    /// GPTEditorTool的自定义Inspector - Azure版本
    /// </summary>
    [CustomEditor(typeof(GPTEditorTool))]
    public class GPTEditorToolEditor : UnityEditor.Editor
    {
        private Vector2 responseScrollPosition = Vector2.zero;
        private GUIStyle responseStyle;
        private bool showAdvancedSettings = false;
        private bool showPrompt = true;
        
        public override void OnInspectorGUI()
        {
            GPTEditorTool tool = (GPTEditorTool)target;
            
            // 初始化样式
            if (responseStyle == null)
            {
                responseStyle = new GUIStyle(EditorStyles.textArea);
                responseStyle.wordWrap = true;
                responseStyle.fontSize = 12;
            }
            
            EditorGUILayout.LabelField("Azure OpenAI Editor调用工具", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // Azure配置
            //EditorGUILayout.LabelField("Azure OpenAI 配置", EditorStyles.boldLabel);
            var azureEndpointProperty = serializedObject.FindProperty("azureEndpoint");
            var deploymentNameProperty = serializedObject.FindProperty("deploymentName");
            var apiKeyProperty = serializedObject.FindProperty("apiKey");
            var apiVersionProperty = serializedObject.FindProperty("apiVersion");
            
            EditorGUILayout.PropertyField(azureEndpointProperty, new GUIContent("Azure Endpoint"));
            EditorGUILayout.PropertyField(deploymentNameProperty, new GUIContent("Deployment Name"));
            EditorGUILayout.PropertyField(apiKeyProperty, new GUIContent("API Key"));
            EditorGUILayout.PropertyField(apiVersionProperty, new GUIContent("API Version"));
            
            // 连接测试按钮
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("测试连接配置", GUILayout.Height(25)))
            {
                tool.TestConnection();
            }
            if (GUILayout.Button("重置为默认", GUILayout.Width(100)))
            {
                tool.ResetToDefaults();
            }
            EditorGUILayout.EndHorizontal();
            
            // 高级设置折叠面板
            showAdvancedSettings = EditorGUILayout.Foldout(showAdvancedSettings, "模型参数");
            if (showAdvancedSettings)
            {
                EditorGUI.indentLevel++;
                var temperatureProperty = serializedObject.FindProperty("temperature");
                var maxTokensProperty = serializedObject.FindProperty("maxTokens");
                var topPProperty = serializedObject.FindProperty("topP");
                var frequencyPenaltyProperty = serializedObject.FindProperty("frequencyPenalty");
                var presencePenaltyProperty = serializedObject.FindProperty("presencePenalty");
                
                EditorGUILayout.PropertyField(temperatureProperty, new GUIContent("Temperature"));
                EditorGUILayout.PropertyField(maxTokensProperty, new GUIContent("最大Token数"));
                EditorGUILayout.PropertyField(topPProperty, new GUIContent("Top P"));
                EditorGUILayout.PropertyField(frequencyPenaltyProperty, new GUIContent("频率惩罚"));
                EditorGUILayout.PropertyField(presencePenaltyProperty, new GUIContent("存在惩罚"));
                EditorGUI.indentLevel--;
            }
            

            showPrompt = EditorGUILayout.Foldout(showPrompt, "系统提示");
            if (showPrompt)
            {
                // 系统提示
                // EditorGUILayout.LabelField("系统提示", EditorStyles.boldLabel);
                EditorGUI.indentLevel++;
                var systemPromptProperty = serializedObject.FindProperty("systemPrompt");
                EditorGUILayout.PropertyField(systemPromptProperty, GUIContent.none, GUILayout.Height(320));
                EditorGUI.indentLevel--;
            }
            
            // 用户输入
          //  EditorGUILayout.LabelField("用户输入", EditorStyles.boldLabel);
            var userPromptProperty = serializedObject.FindProperty("userPrompt");
            EditorGUILayout.PropertyField(userPromptProperty, GUIContent.none, GUILayout.Height(120));
            
            // 操作按钮
            EditorGUILayout.LabelField("操作", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            
            // 发送按钮
            var isProcessingProperty = serializedObject.FindProperty("isProcessing");
            GUI.enabled = !isProcessingProperty.boolValue && !string.IsNullOrEmpty(userPromptProperty.stringValue.Trim());
            
            if (GUILayout.Button("发送到Azure GPT", GUILayout.Height(30)))
            {
                tool.SendToGPT();
            }
            
            GUI.enabled = true;
            
            // 清空按钮
            if (GUILayout.Button("清空响应", GUILayout.Width(80)))
            {
                tool.ClearResponse();
            }
            
            EditorGUILayout.EndHorizontal();
            
            // 状态显示
            if (isProcessingProperty.boolValue)
            {
                EditorGUILayout.Space();
                EditorGUILayout.HelpBox("正在处理请求，请稍候...", MessageType.Info);
                
                // 显示进度条动画
                Rect progressRect = GUILayoutUtility.GetRect(0, 20, GUILayout.ExpandWidth(true));
                EditorGUI.ProgressBar(progressRect, Mathf.PingPong(Time.realtimeSinceStartup, 1), "发送中...");
                
                // 强制重绘以显示动画
                EditorUtility.SetDirty(target);
                Repaint();
            }
            
            // 错误信息显示
            var lastErrorProperty = serializedObject.FindProperty("lastError");
            if (!string.IsNullOrEmpty(lastErrorProperty.stringValue))
            {
                EditorGUILayout.Space();
                EditorGUILayout.HelpBox($"错误：{lastErrorProperty.stringValue}", MessageType.Error);
            }
            
            // 性能统计显示
            var lastRequestTimeProperty = serializedObject.FindProperty("lastRequestTime");
            var performanceStatsProperty = serializedObject.FindProperty("performanceStats");
            
            if (lastRequestTimeProperty.floatValue > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("性能统计", EditorStyles.boldLabel);
                
                // 简化的性能显示
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"上次请求耗时: {lastRequestTimeProperty.floatValue:F2} 秒", EditorStyles.helpBox);
                
                // 性能指示器颜色
                Color originalColor = GUI.color;
                if (lastRequestTimeProperty.floatValue < 2.0f)
                    GUI.color = Color.green;
                else if (lastRequestTimeProperty.floatValue < 5.0f)
                    GUI.color = Color.yellow;
                else
                    GUI.color = Color.red;
                    
                EditorGUILayout.LabelField("●", GUILayout.Width(20));
                GUI.color = originalColor;
                EditorGUILayout.EndHorizontal();
                
                // 详细统计（可折叠）
                if (!string.IsNullOrEmpty(performanceStatsProperty.stringValue))
                {
                    var foldoutKey = "PerformanceStatsFoldout";
                    bool showDetails = EditorPrefs.GetBool(foldoutKey, false);
                    showDetails = EditorGUILayout.Foldout(showDetails, "详细统计");
                    EditorPrefs.SetBool(foldoutKey, showDetails);
                    
                    if (showDetails)
                    {
                        EditorGUILayout.SelectableLabel(performanceStatsProperty.stringValue, EditorStyles.helpBox, GUILayout.Height(80));
                    }
                }
            }
            
            EditorGUILayout.Space();
            
            // API响应显示
            EditorGUILayout.LabelField("API响应", EditorStyles.boldLabel);
            var apiResponseProperty = serializedObject.FindProperty("apiResponse");
            
            // 使用滚动区域显示响应
            responseScrollPosition = EditorGUILayout.BeginScrollView(responseScrollPosition, GUILayout.Height(640));
            EditorGUILayout.SelectableLabel(apiResponseProperty.stringValue, responseStyle, GUILayout.ExpandHeight(true));
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.Space();
            
            // 使用说明
            EditorGUILayout.HelpBox(
                "Azure OpenAI 使用说明：\n" +
                "1. 配置Azure Endpoint、Deployment和API Key\n" +
                "2. 调整模型参数（Temperature控制随机性，Top P控制多样性）\n" +
                "3. 点击\"测试连接配置\"验证设置\n" +
                "4. 编辑系统提示和用户输入\n" +
                "5. 点击\"发送到Azure GPT\"按钮\n" +
                "6. 查看API响应、Token使用和耗时统计", 
                MessageType.Info);
                
            // 参数说明
            var parameterFoldoutKey = "ParameterExplanationFoldout";
            bool showParameterHelp = EditorPrefs.GetBool(parameterFoldoutKey, false);
            showParameterHelp = EditorGUILayout.Foldout(showParameterHelp, "📖 参数说明");
            EditorPrefs.SetBool(parameterFoldoutKey, showParameterHelp);
            
            if (showParameterHelp)
            {
                EditorGUILayout.HelpBox(
                    "📊 模型参数详解：\n\n" +
                    "🎯 Temperature (0.0-2.0): 控制输出随机性\n" +
                    "  • 0.0-0.3: 精确、一致（技术文档）\n" +
                    "  • 0.7: 平衡（推荐）\n" +
                    "  • 1.0-2.0: 创造性（创意写作）\n\n" +
                    "🎲 Top P (0.0-1.0): 控制词汇选择范围\n" +
                    "  • 0.1: 保守选择\n" +
                    "  • 0.95: 平衡（推荐）\n" +
                    "  • 1.0: 最大多样性\n\n" +
                    "🔄 Frequency Penalty (-2.0 to 2.0): 减少重复词汇\n" +
                    "  • 0.0: 自然状态\n" +
                    "  • 0.5: 轻微减少重复\n" +
                    "  • 1.0: 强烈避免重复\n\n" +
                    "🆕 Presence Penalty (-2.0 to 2.0): 鼓励新话题\n" +
                    "  • 0.0: 不限制\n" +
                    "  • 0.3: 鼓励新话题（推荐）\n" +
                    "  • 0.6: 强烈鼓励新内容",
                    MessageType.None);
            }
            
            // 应用属性更改
            serializedObject.ApplyModifiedProperties();
        }
    }
}
#endif 