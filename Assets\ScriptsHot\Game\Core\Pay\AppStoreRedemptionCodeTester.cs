/*
 ****************************************************
 * 作者：AI Assistant
 * 创建时间：2025/07/30
 * 功能：App Store兑换码功能测试工具
 ****************************************************
 */

using UnityEngine;
using System.Collections;

namespace Game
{
    /// <summary>
    /// App Store兑换码功能测试工具
    /// 用于测试兑换码兑换后会员状态是否正确更新
    /// </summary>
    public class AppStoreRedemptionCodeTester : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private float checkInterval = 5f; // 检查间隔（秒）
        
        private bool isTestingActive = false;
        private Coroutine testCoroutine;

        private void Start()
        {
            if (enableDebugLogs)
            {
                VFDebug.Log("App Store兑换码测试工具已启动");
            }
        }

        /// <summary>
        /// 开始测试兑换码功能
        /// </summary>
        [ContextMenu("开始测试兑换码功能")]
        public void StartRedemptionCodeTest()
        {
            if (isTestingActive)
            {
                VFDebug.LogWarning("测试已在进行中");
                return;
            }

            VFDebug.Log("=== 开始App Store兑换码功能测试 ===");
            isTestingActive = true;
            testCoroutine = StartCoroutine(TestRedemptionCodeFlow());
        }

        /// <summary>
        /// 停止测试
        /// </summary>
        [ContextMenu("停止测试")]
        public void StopTest()
        {
            if (testCoroutine != null)
            {
                StopCoroutine(testCoroutine);
                testCoroutine = null;
            }
            isTestingActive = false;
            VFDebug.Log("=== 测试已停止 ===");
        }

        /// <summary>
        /// 手动触发订阅状态检查
        /// </summary>
        [ContextMenu("手动检查订阅状态")]
        public void ManualCheckSubscriptionStatus()
        {
            VFDebug.Log("=== 手动触发订阅状态检查 ===");
            
            if (PurchasingManager.instance != null)
            {
                PurchasingManager.instance.CheckAllSubscriptionStatus((foundActiveSubscription) =>
                {
                    if (foundActiveSubscription)
                    {
                        VFDebug.Log("✅ 发现有效订阅，会员状态应该会更新");
                    }
                    else
                    {
                        VFDebug.Log("❌ 未发现有效订阅");
                    }
                });
            }
            else
            {
                VFDebug.LogError("❌ PurchasingManager未初始化");
            }
        }

        /// <summary>
        /// 检查当前会员状态
        /// </summary>
        [ContextMenu("检查当前会员状态")]
        public void CheckCurrentMemberStatus()
        {
            VFDebug.Log("=== 检查当前会员状态 ===");
            
            var mainModel = ModelManager.instance.GetModel<MainModel>(ModelConsts.Main);
            if (mainModel != null && mainModel.incentiveData != null)
            {
                var memberInfo = mainModel.incentiveData.homepage_economic_info?.member_info;
                if (memberInfo != null)
                {
                    VFDebug.Log($"会员类型: {memberInfo.member_type}");
                    VFDebug.Log($"订阅状态: {memberInfo.SubscribeStatus}");
                    VFDebug.Log($"是否为会员: {memberInfo.is_member}");
                    VFDebug.Log($"过期时间: {memberInfo.expire_time}");
                }
                else
                {
                    VFDebug.LogWarning("会员信息为空");
                }
            }
            else
            {
                VFDebug.LogWarning("主模型或激励数据为空");
            }
        }

        /// <summary>
        /// 测试兑换码流程
        /// </summary>
        private IEnumerator TestRedemptionCodeFlow()
        {
            VFDebug.Log("1. 记录测试开始时的会员状态");
            CheckCurrentMemberStatus();
            
            yield return new WaitForSeconds(2f);
            
            VFDebug.Log("2. 提示用户在App Store中兑换会员码");
            VFDebug.Log("请在App Store中兑换会员码，然后返回应用");
            
            // 等待用户操作
            yield return new WaitForSeconds(10f);
            
            VFDebug.Log("3. 开始定期检查订阅状态");
            
            int checkCount = 0;
            int maxChecks = 12; // 最多检查12次（1分钟）
            
            while (checkCount < maxChecks && isTestingActive)
            {
                checkCount++;
                VFDebug.Log($"第 {checkCount} 次检查订阅状态...");
                
                ManualCheckSubscriptionStatus();
                
                yield return new WaitForSeconds(checkInterval);
                
                VFDebug.Log($"检查会员状态变化...");
                CheckCurrentMemberStatus();
                
                yield return new WaitForSeconds(1f);
            }
            
            VFDebug.Log("=== 测试完成 ===");
            isTestingActive = false;
        }

        /// <summary>
        /// 模拟应用从后台恢复
        /// </summary>
        [ContextMenu("模拟应用从后台恢复")]
        public void SimulateAppResume()
        {
            VFDebug.Log("=== 模拟应用从后台恢复 ===");
            
            var mainController = ControllerManager.instance.GetController<MainController>(ModelConsts.Main);
            if (mainController != null)
            {
                // 模拟OnApplicationPause(false)
                mainController.OnApplicationPause(false);
                VFDebug.Log("已触发应用恢复逻辑");
            }
            else
            {
                VFDebug.LogError("MainController未找到");
            }
        }

        private void OnDestroy()
        {
            StopTest();
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (enableDebugLogs)
            {
                VFDebug.Log($"应用暂停状态变化: {pauseStatus}");
                if (!pauseStatus)
                {
                    VFDebug.Log("应用从后台恢复，将检查订阅状态");
                }
            }
        }
    }
}
