/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreTabBtn : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreTabBtn";

        public Controller btnCtrl;
        public GTextField txt;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            btnCtrl = com.GetControllerAt(0);
            txt = (GTextField)com.GetChildAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            btnCtrl = null;
            txt = null;
        }
    }
}