/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreProDescribe : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreProDescribe";

        public Controller ctrl;
        public Controller ctrlDescribeBack;
        public Controller ctrlProCount;
        public GTextField txtTitle;
        public GTextField txtDesc;
        public GGroup groupDesc;
        public GTextField txtTypeName;
        public GGroup comTitle;
        public GGraph imgLevelBack;
        public GTextField txtLevel;
        public GGroup comPro;
        public ExploreStoryPro pro1;
        public ExploreStoryPro pro2;
        public ExploreStoryPro pro3;
        public ExploreStoryPro pro4;
        public ExploreStoryPro pro5;
        public GGroup proGroup;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrl = com.GetControllerAt(0);
            ctrlDescribeBack = com.GetControllerAt(1);
            ctrlProCount = com.GetControllerAt(2);
            txtTitle = (GTextField)com.GetChildAt(6);
            txtDesc = (GTextField)com.GetChildAt(7);
            groupDesc = (GGroup)com.GetChildAt(8);
            txtTypeName = (GTextField)com.GetChildAt(11);
            comTitle = (GGroup)com.GetChildAt(13);
            imgLevelBack = (GGraph)com.GetChildAt(14);
            txtLevel = (GTextField)com.GetChildAt(15);
            comPro = (GGroup)com.GetChildAt(16);
            pro1 = new ExploreStoryPro();
            pro1.Construct(com.GetChildAt(18).asCom);
            pro2 = new ExploreStoryPro();
            pro2.Construct(com.GetChildAt(19).asCom);
            pro3 = new ExploreStoryPro();
            pro3.Construct(com.GetChildAt(20).asCom);
            pro4 = new ExploreStoryPro();
            pro4.Construct(com.GetChildAt(21).asCom);
            pro5 = new ExploreStoryPro();
            pro5.Construct(com.GetChildAt(22).asCom);
            proGroup = (GGroup)com.GetChildAt(23);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrl = null;
            ctrlDescribeBack = null;
            ctrlProCount = null;
            txtTitle = null;
            txtDesc = null;
            groupDesc = null;
            txtTypeName = null;
            comTitle = null;
            imgLevelBack = null;
            txtLevel = null;
            comPro = null;
            pro1.Dispose();
            pro1 = null;
            pro2.Dispose();
            pro2 = null;
            pro3.Dispose();
            pro3 = null;
            pro4.Dispose();
            pro4 = null;
            pro5.Dispose();
            pro5 = null;
            proGroup = null;
        }
    }
}