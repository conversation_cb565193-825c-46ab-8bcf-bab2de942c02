/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreMemoryItem : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreMemoryItem";

        public Controller ctrl;
        public Controller ctrlDescribeBack;
        public GTextField txtTitle;
        public GTextField txtDesc;
        public GGroup groupDesc;
        public GTextField txtTypeName;
        public GGroup comTitle;
        public GGraph imgLevelBack;
        public GTextField txtLevel;
        public GGroup comPro;
        public GGraph btnContinue;
        public GTextField btnTxt;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrl = com.GetControllerAt(0);
            ctrlDescribeBack = com.GetControllerAt(1);
            txtTitle = (GTextField)com.GetChildAt(6);
            txtDesc = (GTextField)com.GetChildAt(7);
            groupDesc = (GGroup)com.GetChildAt(8);
            txtTypeName = (GTextField)com.GetChildAt(11);
            comTitle = (GGroup)com.GetChildAt(13);
            imgLevelBack = (GGraph)com.GetChildAt(14);
            txtLevel = (GTextField)com.GetChildAt(15);
            comPro = (GGroup)com.GetChildAt(16);
            btnContinue = (GGraph)com.GetChildAt(18);
            btnTxt = (GTextField)com.GetChildAt(19);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrl = null;
            ctrlDescribeBack = null;
            txtTitle = null;
            txtDesc = null;
            groupDesc = null;
            txtTypeName = null;
            comTitle = null;
            imgLevelBack = null;
            txtLevel = null;
            comPro = null;
            btnContinue = null;
            btnTxt = null;
        }
    }
}