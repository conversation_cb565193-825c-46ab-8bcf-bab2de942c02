/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreSettlement
{
    public partial class ExploreSettlementPanel : UIBindT
    {
        public override string pkgName => "ExploreSettlement";
        public override string comName => "ExploreSettlementPanel";

        public GGraph imgBG;
        public GList list;
        public GLoader mask;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GGraph)com.GetChildAt(0);
            list = (GList)com.GetChildAt(1);
            mask = (GLoader)com.GetChildAt(2);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            list = null;
            mask = null;
        }
    }
}