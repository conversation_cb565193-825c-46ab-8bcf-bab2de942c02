/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreNoticeBtn : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreNoticeBtn";

        public Controller ctrl;
        public GGroup btnDown;
        public GImage btnUp;
        public GGroup btnUp_2;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrl = com.GetControllerAt(0);
            btnDown = (GGroup)com.GetChildAt(2);
            btnUp = (GImage)com.GetChildAt(3);
            btnUp_2 = (GGroup)com.GetChildAt(4);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrl = null;
            btnDown = null;
            btnUp = null;
            btnUp_2 = null;
        }
    }
}