﻿using System.Collections.Generic;
using FairyGUI;
using ScriptsHot.Game.Modules.Explore;
using UIBind.Explore.Item;
using UnityEngine;

public partial class ExplorePanelUI
{
    private GList _friendList;
    private int currentIndex_friend = 0;
    private int _currentDisplayIndex_friend = -1;  // 记录当前显示的Item索引
    private Dictionary<int, ExploreFriendItemUI> _items_friend = new Dictionary<int, ExploreFriendItemUI>();
    
    public void InitFriendUI()
    {
        _friendList = ui.comFriendContainer.listContainer;
        _friendList.SetVirtual();
        _friendList.itemRenderer = RenderFriendItem;
        _friendList.scrollPane.snapToItem = true; 
        _friendList.scrollPane.pageMode = true;   
        //降低惯性，让短距离滑动也能翻页
        _friendList.scrollPane.decelerationRate = 0.5f;  
        //设置滚动步长，避免滑动太大翻多页
        _friendList.scrollPane.scrollStep = _list.scrollPane.viewHeight / 7; 
        _friendList.scrollPane.onScrollEnd.Add(OnScrollEnd);

        //这个延迟是 给ui.imgBG 留时间
        TimerManager.instance.RegisterNextFrame((a) =>
        {
            //按注释 有些分辨率顶部会有白边
            _friendList.y = ui.imgBG.y;
            _friendList.height = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;//高度不能等于imgBG的高 不知道为啥
        });
        
        this._friendList.scrollPane.onScroll.Add(friend_scrolled);
    }
    
    private async void RenderFriendItem(int index, GObject obj)
    {
        // 如果是当前显示的Item，跳过刷新
        if (index == _currentDisplayIndex_friend)
        {
            return;
        }
        GComponent modelItem = obj.asCom;
        List<ExploreItemData> datas = _controller.Model.allMissionData; 
        // Debug.LogError("Index:::" + index + "     entityId ::" + datas[index].Data.entityId + "  avatarId:::" + datas[index].Data.dialogTaskPreloadData.avatar.avatarId);

        if (modelItem.data == null)
        {
            ExploreFriendItemUI uiItem = new ExploreFriendItemUI(modelItem, _controller);
            uiItem.SetUI(this.ui.com);
            modelItem.data = uiItem;
        }
        datas[index].HasLook = true;
        ExploreFriendItemUI modelItemEx = modelItem.data as ExploreFriendItemUI;
        _items_friend[index] = modelItemEx;
        modelItemEx.SetData(datas[index].Data);
        modelItemEx.UpdateTitle(datas[index].Data.avatar);
        modelItemEx.UpdateDescribe(datas[index].Data.detail,datas[index].Data.storyId);

        if (_exploreItemDic_friend.ContainsKey(modelItemEx))
        {
            //循环列表重用，重设index
            _exploreItemDic_friend[modelItemEx] = index;
        }
        else
        {
            //新增
            _exploreItemDic_friend.Add(modelItemEx,index);
        }

        bool is3d = true;//datas[index].Data.avatar.is3D;
        modelItemEx.SetIs3d(is3d);
        if (is3d)
        {
            // Debug.LogError("+渲染了物体"+ index);
            modelItemEx.SetModel(_gameScene,datas[index].Data.storyId,datas[index].Data.avatar.avatarId,datas[index].Data.scene.bgPicTag);  // 绑定新模型
            await modelItemEx.LoadBackgroundImageAsync2(datas[index].Data.scene.bgPicTag);
        }
        else
        {
            modelItemEx.ShowImg2d(datas[index].Data.scene.bgPicUrl);
        }
    }
    private void AddFriendEvent()
    {
    }
    
    private void RemoveFriendEvent()
    {
  
    }
    
        #region 思明处理相机
    
    private void friend_scrolled(EventContext context)
    {
        var y = _friendList.scrollPane.scrollingPosY;
        UpdateScrollDirection_friend(_friendList);
    }

    //注意：现在一屏只有一个元素，后续入果一屏多个元素，需要基于元素个数甚至单个元素大小来维护此list。
    private void UpdateItemToRender_friend()
    {
        int startIndex = _list.GetFirstChildInView();
        _itemToRender_friend.Clear();
        
        if (_isDragging_friend)
        {
            if (_isScrollingDown_friend)
            {
                _itemToRender_friend.Add(startIndex);
                _itemToRender_friend.Add(startIndex + 1);
                //show 2 cams
            }
            else
            {
                _itemToRender_friend.Add(startIndex);
                _itemToRender_friend.Add(startIndex + 1);
                //show 2 cams
            }
        }
        else
        {
            _itemToRender_friend.Add(startIndex);
            //show 1 cam
        }
    }

    private void Update3DRendering_friend()
    {
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (hCon.IsExploreTab())
        {
            UpdateItemToRender_friend();
            foreach (var kvp  in _exploreItemDic_friend)
            {
                if (kvp.Key != null)
                {
                    if (_itemToRender_friend.Contains(kvp.Value))
                    {
                        kvp.Key.SetCamera(true);
                        kvp.Key.SetAvatar(true);
                    }
                    else
                    {
                        kvp.Key.SetCamera(false);
                        kvp.Key.SetAvatar(false);
                    }
                }
            }
        }
        else
        {
            foreach (var kvp  in _exploreItemDic_friend)
            {
                if (kvp.Key != null)
                {
                    kvp.Key.SetCamera(false,999);
                    kvp.Key.SetAvatar(false,999);
                }
            }
        }
        
    }
    
    private Dictionary<ExploreFriendItemUI,int> _exploreItemDic_friend = new();
    private List<int> _itemToRender_friend = new List<int>(); //要渲染的index
    private float _lastScrollPosition_friend = 0;
    private bool _isScrollingDown_friend = false;
    private bool _isDragging_friend = false;
    

    private void UpdateScrollDirection_friend(GList list)
    {
        float currentPos = list.scrollPane.scrollingPosY;
        _isScrollingDown_friend = currentPos > _lastScrollPosition_friend;
        _lastScrollPosition_friend = currentPos;
    
        // Debug.LogError($"滚动方向: {(_isScrollingDown_friend ? "向下" : "向上")}");
    }
    
    #endregion
}