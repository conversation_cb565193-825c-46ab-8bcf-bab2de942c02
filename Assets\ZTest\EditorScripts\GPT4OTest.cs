﻿#if UNITY_EDITOR
namespace ZTest.ZTemp
{
    using System.Collections;
    using UnityEngine;
    using UnityEngine.Networking;
    using System.Text;

    public class GPTCaller : MonoBehaviour
    {
        private readonly string apiKey = "d730676b773048c691776c89b5e8941b";  // 推荐从环境变量或加密配置读取
        
        private static readonly string system = "Hello, this is a test, please answer the following questions.";

        void Start()
        {
           // StartCoroutine(SendToGPT("Hello, GPT-4o! What's the weather like on Mars?"));
        }

        IEnumerator SendToGPT(string prompt)
        {
            string url = "https://api.openai.com/v1/chat/completions";

            var requestData = new
            {
                model = "gpt-4o", // 或 "gpt-4"
                messages = new object[]
                {
                    new { role = "system", content = system},
                    new { role = "user", content = prompt }
                },
                temperature = 0.7,
                max_tokens = 1000
            };

            string jsonBody = JsonUtility.ToJson(requestData);  // 简单结构 OK，但推荐用 JSON.NET

            // 修复 Unity 内置 JsonUtility 无法处理数组的问题
            // 替代代码（使用 Newtonsoft.Json）：
            // string jsonBody = JsonConvert.SerializeObject(requestData);

            using (UnityWebRequest request = new UnityWebRequest(url, "POST"))
            {
                byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonBody);
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("Authorization", "Bearer " + apiKey);

                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    Debug.Log("GPT-4o Response:\n" + request.downloadHandler.text);
                }
                else
                {
                    Debug.LogError("Request Error: " + request.error);
                }
            }
        }
    }

}
#endif