/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreSettlement
{
    public partial class CompSettleExploreNextItem : UIBindT
    {
        public override string pkgName => "ExploreSettlement";
        public override string comName => "CompSettleExploreNextItem";

        public GTextField tfNext;
        public GTextField tfTitle;
        public GTextField tfDesc;
        public GGraph btnStart;
        public GTextField tfStart;
        public Transition show;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tfNext = (GTextField)com.GetChildAt(1);
            tfTitle = (GTextField)com.GetChildAt(4);
            tfDesc = (GTextField)com.GetChildAt(5);
            btnStart = (GGraph)com.GetChildAt(6);
            tfStart = (GTextField)com.GetChildAt(7);
            show = com.GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tfNext = null;
            tfTitle = null;
            tfDesc = null;
            btnStart = null;
            tfStart = null;
            show = null;
        }
    }
}