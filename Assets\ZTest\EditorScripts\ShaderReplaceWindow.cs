#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;

namespace MeEditorFramework
{
    public class ShaderReplaceWindow : EditorWindow
    {
        [MenuItem("Tools/MeEditor/Shader替换工具")]
        static void OpenShaderReplaceWindow()
        {
            ShaderReplaceWindow window = (ShaderReplaceWindow)EditorWindow
                .GetWindow(typeof(ShaderReplaceWindow), false, "Shader替换工具", true);
            window.Show();
        }

        enum PageId
        {
            ShaderReplace
        }

        private PageId mCurPageId;
        private Shader sourceShader; // 要查找的shader A
        private Shader targetShader; // 要替换的shader B
        private List<Material> foundMaterials = new List<Material>();
        private Vector2 scrollPos = new Vector2();
        private bool isSearching = false;

        private void OnGUI()
        {
            mCurPageId = (PageId)GUILayout.Toolbar((int)mCurPageId, Enum.GetNames(typeof(PageId)));
            if (mCurPageId == PageId.ShaderReplace)
            {
                ShaderReplaceInit();
            }
        }

        private void ShaderReplaceInit()
        {
            EditorGUILayout.BeginVertical();

            // 标题
            EditorGUILayout.BeginHorizontal();
            {
                EditorGUILayout.LabelField("Shader替换工具", EditorStyles.boldLabel);
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space();

            // 源shader选择
            EditorGUILayout.BeginHorizontal();
            {
                EditorGUILayout.LabelField("要查找的Shader:", GUILayout.Width(100));
                sourceShader = (Shader)EditorGUILayout.ObjectField(sourceShader, typeof(Shader), false);
            }
            EditorGUILayout.EndHorizontal();

            // 目标shader选择
            EditorGUILayout.BeginHorizontal();
            {
                EditorGUILayout.LabelField("替换为Shader:", GUILayout.Width(100));
                targetShader = (Shader)EditorGUILayout.ObjectField(targetShader, typeof(Shader), false);
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // 查找按钮
            EditorGUILayout.BeginHorizontal();
            {
                GUI.enabled = sourceShader != null && !isSearching;
                if (GUILayout.Button("查找使用该Shader的材质球", GUILayout.MinWidth(150), GUILayout.MinHeight(30)))
                {
                    FindMaterialsWithShader();
                }
                GUI.enabled = true;
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // 显示搜索状态
            if (isSearching)
            {
                EditorGUILayout.LabelField("正在搜索中...", EditorStyles.helpBox);
            }

            // 显示结果统计
            EditorGUILayout.LabelField($"找到的材质球数量: {foundMaterials.Count}");

            EditorGUILayout.Space();

            // 替换按钮区域
            EditorGUILayout.LabelField("批量操作区域", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            {
                GUI.enabled = foundMaterials.Count > 0 && targetShader != null && !isSearching;
                if (GUILayout.Button("批量替换Shader", GUILayout.MinWidth(120), GUILayout.MinHeight(30)))
                {
                    if (EditorUtility.DisplayDialog("确认替换", 
                        $"确定要将 {foundMaterials.Count} 个材质球的Shader从\n'{sourceShader.name}'\n替换为\n'{targetShader.name}' 吗？", 
                        "确定", "取消"))
                    {
                        ReplaceShaders();
                    }
                }
                GUI.enabled = true;
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // 显示找到的材质球列表
            EditorGUILayout.LabelField("找到的材质球列表:", EditorStyles.boldLabel);
            scrollPos = EditorGUILayout.BeginScrollView(scrollPos, GUILayout.ExpandHeight(true));
            {
                for (int i = 0; i < foundMaterials.Count; i++)
                {
                    EditorGUILayout.BeginHorizontal();
                    {
                        EditorGUILayout.ObjectField(foundMaterials[i], typeof(Material), false);
                        
                        // 显示材质球路径
                        string path = AssetDatabase.GetAssetPath(foundMaterials[i]);
                        EditorGUILayout.LabelField(path, EditorStyles.miniLabel);
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }
            EditorGUILayout.EndScrollView();

            EditorGUILayout.EndVertical();
        }

        private void FindMaterialsWithShader()
        {
            if (sourceShader == null)
            {
                EditorUtility.DisplayDialog("错误", "请先选择要查找的Shader", "确定");
                return;
            }

            isSearching = true;
            foundMaterials.Clear();
            
            try
            {
                // 查找所有材质球
                string[] materialGuids = AssetDatabase.FindAssets("t:Material", new[] { "Assets" });
                
                float totalCount = materialGuids.Length;
                float currentCount = 0;

                foreach (string guid in materialGuids)
                {
                    currentCount++;
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    
                    // 显示进度条
                    bool canceled = EditorUtility.DisplayCancelableProgressBar(
                        "搜索材质球", 
                        $"正在检查: {Path.GetFileName(path)}", 
                        currentCount / totalCount);
                        
                    if (canceled)
                    {
                        break;
                    }

                    Material material = AssetDatabase.LoadAssetAtPath<Material>(path);
                    if (material != null && material.shader == sourceShader)
                    {
                        foundMaterials.Add(material);
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                isSearching = false;
            }

            Debug.Log($"搜索完成，找到 {foundMaterials.Count} 个使用 '{sourceShader.name}' 的材质球");
        }

        private void ReplaceShaders()
        {
            if (foundMaterials.Count == 0 || targetShader == null)
            {
                EditorUtility.DisplayDialog("错误", "没有可替换的材质球或目标Shader为空", "确定");
                return;
            }

            int successCount = 0;
            float totalCount = foundMaterials.Count;

            try
            {
                for (int i = 0; i < foundMaterials.Count; i++)
                {
                    Material material = foundMaterials[i];
                    
                    // 显示进度条
                    bool canceled = EditorUtility.DisplayCancelableProgressBar(
                        "替换Shader", 
                        $"正在替换: {material.name}", 
                        (float)i / totalCount);
                        
                    if (canceled)
                    {
                        break;
                    }

                    if (material != null)
                    {
                        // 标记为脏数据，确保保存
                        EditorUtility.SetDirty(material);
                        
                        // 替换shader
                        material.shader = targetShader;
                        successCount++;
                        
                        Debug.Log($"已替换材质球: {AssetDatabase.GetAssetPath(material)}", material);
                    }
                }

                // 保存资源
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }

            EditorUtility.DisplayDialog("替换完成", 
                $"成功替换了 {successCount} 个材质球的Shader！", "确定");

            // 重新查找以更新列表
            if (successCount > 0)
            {
                FindMaterialsWithShader();
            }
        }
    }
}
#endif 