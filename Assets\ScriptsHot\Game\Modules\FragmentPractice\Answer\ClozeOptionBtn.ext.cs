using System;
using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ClozeOptionBtn : GButton
    {
        public ClozeOptionBtn()
        {
            onAddedToStage.Add(OnAddedToStage);
        }

        private void OnAddedToStage(EventContext context)
        {
            ctrlColor.onChanged.Add(OnChangeColor);
        }

        private void OnChangeColor(EventContext context)
        {
            var text = GetChild("title") as GTextField;
            var format = text.textFormat;
            switch (ctrlColor.selectedPage)
            {
                case "green":
                case "red":
                    format.font = FontCfg.DinNextBold;
                    break;
                default:
                    format.font = FontCfg.DinNextRegular;
                    break;
            }

            text.textFormat = format;
        }
    }
}