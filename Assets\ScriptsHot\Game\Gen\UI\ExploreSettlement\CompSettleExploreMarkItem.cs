/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreSettlement
{
    public partial class CompSettleExploreMarkItem : UIBindT
    {
        public override string pkgName => "ExploreSettlement";
        public override string comName => "CompSettleExploreMarkItem";

        public GTextField tfTitle;
        public GList listMark;
        public GGroup grp;
        public Transition show;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tfTitle = (GTextField)com.GetChildAt(1);
            listMark = (GList)com.GetChildAt(2);
            grp = (GGroup)com.GetChildAt(3);
            show = com.GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tfTitle = null;
            listMark = null;
            grp = null;
            show = null;
        }
    }
}