#if UNITY_EDITOR
using System;
using System.IO;
using System.Text;
using System.Collections.Generic;
using UnityEngine;
using AnimationSystem;
using Newtonsoft.Json;
using UnityEditor;

namespace ZTest
{
    /// <summary>
    /// 音频导出数据类
    /// 用于序列化和反序列化音频相关数据
    /// </summary>
    [Serializable]
    public class AudioExportData
    {
        public int seed;
        public string path;
        public List<SingleSentenceInfo> dialogData;
        public string avatarId;
        public int animTypeId;
        public string fullText;
        
        public AudioExportData()
        {
            dialogData = new List<SingleSentenceInfo>();
        }
    }

    /// <summary>
    /// AudioClip导出器
    /// 用于将DialogManager中的AudioClip保存为WAV文件，并保存相关数据为JSON文件
    /// </summary>
    public class AudioClipExporter : MonoBehaviour
    {
        [Header("导出设置")]
        [SerializeField] private string exportFileName = "exported_audio.wav";
        
        private DialogManager dialogManager;
        
        private void Awake()
        {
            // 获取同GameObject上的DialogManager组件
            dialogManager = GetComponent<DialogManager>();
            if (dialogManager == null)
            {
                Debug.LogWarning($"AudioClipExporter: 在GameObject '{gameObject.name}' 上未找到DialogManager组件！");
            }
        }
        
        /// <summary>
        /// 导出AudioClip为WAV文件并保存数据为JSON文件
        /// </summary>
        public void ExportAudioClip()
        {
            // 兜底判断
            if (dialogManager == null)
            {
                Debug.LogError("AudioClipExporter: DialogManager组件不存在，无法导出音频！");
                return;
            }
            
            if (dialogManager.currentClip == null)
            {
                Debug.LogError("AudioClipExporter: DialogManager.currentClip为空，无法导出音频！");
                return;
            }
            
            try
            {
                // 获取脚本同级目录
               // string scriptPath = GetScriptDirectory();
               string scriptPath = Application.dataPath + "/ZTest/Animation";
                string audioOutputPath = Path.Combine(scriptPath, exportFileName);
                
                // 导出AudioClip为WAV文件
                bool audioSuccess = SaveAudioClipAsWav(dialogManager.currentClip, audioOutputPath);
                
                if (audioSuccess)
                {
                    Debug.Log($"音频导出成功：{audioOutputPath}");
                    
                    // 保存数据为JSON文件
                    string jsonFileName = Path.GetFileNameWithoutExtension(exportFileName) + "-data.json";
                    string jsonOutputPath = Path.Combine(scriptPath, jsonFileName);
                    
                    bool jsonSuccess = SaveDataAsJson(audioOutputPath, jsonOutputPath);
                    
                    if (jsonSuccess)
                    {
                        Debug.Log($"数据导出成功：{jsonOutputPath}");
                    }
                    
#if UNITY_EDITOR
                    // 刷新AssetDatabase以便Unity识别新文件
                    AssetDatabase.Refresh();
                    
                    // 高亮显示导出的音频文件
                    var relativePath = GetRelativeAssetPath(audioOutputPath);
                    var audioAsset = AssetDatabase.LoadAssetAtPath<AudioClip>(relativePath);
                    if (audioAsset != null)
                    {
                        EditorGUIUtility.PingObject(audioAsset);
                    }
#endif
                }
                else
                {
                    Debug.LogError("音频导出失败！");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"导出音频时发生异常：{ex.Message}");
            }
        }
        
        /// <summary>
        /// 保存数据为JSON文件
        /// </summary>
        private bool SaveDataAsJson(string audioFilePath, string jsonOutputPath)
        {
            try
            {
                // 创建导出数据对象
                AudioExportData exportData = new AudioExportData();
                
                // 获取相对路径（相对于Assets目录）
                exportData.path = GetRelativeAssetPath(audioFilePath);
                
                // 获取animTypeId和计算seed
                if (dialogManager.manager != null)
                {
                    exportData.animTypeId = dialogManager.manager.animTypeId;
                    exportData.seed = exportData.animTypeId * 1024 + 3000;
                    
                    // 获取avatarId
                    if (dialogManager.manager.avatarRoot != null && 
                        dialogManager.manager.avatarRoot.avatarData != null)
                    {
                        exportData.avatarId = dialogManager.manager.avatarRoot.avatarData.name;
                    }
                    else
                    {
                        Debug.LogWarning("无法获取avatarId：avatarRoot或avatarData为空");
                        exportData.avatarId = "unknown";
                    }

                    exportData.fullText = dialogManager.currText;
                }
                else
                {
                    Debug.LogWarning("无法获取animTypeId：manager为空");
                    exportData.animTypeId = 0;
                    exportData.seed = 3000;
                    exportData.avatarId = "unknown";
                    exportData.fullText = "";
                }
                
                // 获取当前对话数据
                if (dialogManager.currSentences != null && dialogManager.currSentences.Count > 0)
                {
                    exportData.dialogData = new List<SingleSentenceInfo>(dialogManager.currSentences);
                }
                else
                {
                    Debug.LogWarning("当前对话数据为空");
                    exportData.dialogData = new List<SingleSentenceInfo>();
                }
                
                // 序列化为JSON
                string jsonContent = JsonConvert.SerializeObject(exportData, Formatting.Indented);
                
                // 写入文件
                File.WriteAllText(jsonOutputPath, jsonContent, Encoding.UTF8);
                
                Debug.Log($"JSON数据保存成功：\n" +
                         $"Seed: {exportData.seed}\n" +
                         $"Path: {exportData.path}\n" +
                         $"AvatarId: {exportData.avatarId}\n" +
                         $"AnimTypeId: {exportData.animTypeId}\n" +
                         $"DialogData Count: {exportData.dialogData.Count}");
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"保存JSON文件失败：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 获取脚本所在目录
        /// </summary>
        private string GetScriptDirectory()
        {
#if UNITY_EDITOR
            // 获取脚本文件路径
            var script = MonoScript.FromMonoBehaviour(this);
            string scriptPath = AssetDatabase.GetAssetPath(script);
            return Path.GetDirectoryName(scriptPath);
#else
            // 非Editor环境，返回Application.dataPath
            return Application.dataPath;
#endif
        }
        
        /// <summary>
        /// 获取相对于Assets的路径
        /// </summary>
        private string GetRelativeAssetPath(string absolutePath)
        {
            string assetsPath = Application.dataPath;
            if (absolutePath.StartsWith(assetsPath))
            {
                return "Assets" + absolutePath.Substring(assetsPath.Length);
            }
            return absolutePath;
        }
        
        /// <summary>
        /// 将AudioClip保存为WAV文件
        /// </summary>
        private bool SaveAudioClipAsWav(AudioClip clip, string filePath)
        {
            try
            {
                // 获取音频数据
                float[] samples = new float[clip.samples * clip.channels];
                clip.GetData(samples, 0);
                
                // 转换为16位PCM
                byte[] intData = new byte[samples.Length * 2];
                int rescaleFactor = 32767; // 16位最大值
                
                for (int i = 0; i < samples.Length; i++)
                {
                    // 限制范围到[-1, 1]
                    float sample = Mathf.Clamp(samples[i], -1f, 1f);
                    short intSample = (short)(sample * rescaleFactor);
                    byte[] byteArr = BitConverter.GetBytes(intSample);
                    intData[i * 2] = byteArr[0];
                    intData[i * 2 + 1] = byteArr[1];
                }
                
                // 确保目录存在
                string directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                // 写入WAV文件
                using (FileStream fs = new FileStream(filePath, FileMode.Create))
                using (BinaryWriter writer = new BinaryWriter(fs))
                {
                    // WAV文件头
                    writer.Write(Encoding.ASCII.GetBytes("RIFF"));
                    writer.Write(36 + intData.Length); // 文件大小
                    writer.Write(Encoding.ASCII.GetBytes("WAVE"));
                    
                    // fmt chunk
                    writer.Write(Encoding.ASCII.GetBytes("fmt "));
                    writer.Write(16); // chunk大小
                    writer.Write((ushort)1); // 音频格式 (1 = PCM)
                    writer.Write((ushort)clip.channels); // 声道数
                    writer.Write(clip.frequency); // 采样率
                    writer.Write(clip.frequency * clip.channels * 2); // 字节率
                    writer.Write((ushort)(clip.channels * 2)); // 块对齐
                    writer.Write((ushort)16); // 位深度
                    
                    // data chunk
                    writer.Write(Encoding.ASCII.GetBytes("data"));
                    writer.Write(intData.Length);
                    writer.Write(intData);
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"保存WAV文件失败：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 获取音频信息（用于调试）
        /// </summary>
        public void LogAudioInfo()
        {
            if (dialogManager != null && dialogManager.currentClip != null)
            {
                var clip = dialogManager.currentClip;
                Debug.Log($"音频信息：\n" +
                         $"名称：{clip.name}\n" +
                         $"长度：{clip.length:F2}秒\n" +
                         $"采样率：{clip.frequency}Hz\n" +
                         $"声道数：{clip.channels}\n" +
                         $"样本数：{clip.samples}");
            }
            else
            {
                Debug.LogWarning("无法获取音频信息：DialogManager或currentClip为空");
            }
        }
        
        /// <summary>
        /// 获取当前数据信息（用于调试）
        /// </summary>
        public void LogDataInfo()
        {
            if (dialogManager == null)
            {
                Debug.LogWarning("DialogManager为空");
                return;
            }
            
            string info = "当前数据信息：\n";
            
            if (dialogManager.manager != null)
            {
                info += $"AnimTypeId: {dialogManager.manager.animTypeId}\n";
                info += $"Seed: {dialogManager.manager.animTypeId * 1024 + 3000}\n";
                
                if (dialogManager.manager.avatarRoot != null && 
                    dialogManager.manager.avatarRoot.avatarData != null)
                {
                    info += $"AvatarId：{dialogManager.manager.avatarRoot.avatarData.name}\n";
                }
                else
                {
                    info += "AvatarId：无法获取\n";
                }
            }
            else
            {
                info += "Manager为空\n";
            }
            
            if (dialogManager.currSentences != null)
            {
                info += $"对话数据数量: {dialogManager.currSentences.Count}\n";
            }
            else
            {
                info += "对话数据：无\n";
            }
            
            Debug.Log(info);
        }
    }
}

namespace ZTest.Editor
{
    /// <summary>
    /// AudioClipExporter的自定义Inspector
    /// </summary>
    [CustomEditor(typeof(AudioClipExporter))]
    public class AudioClipExporterEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("音频导出工具", EditorStyles.boldLabel);
            
            AudioClipExporter exporter = (AudioClipExporter)target;
            
            // 显示当前AudioClip信息
            var dialogManager = exporter.GetComponent<DialogManager>();
            if (dialogManager != null && dialogManager.currentClip != null)
            {
                var clip = dialogManager.currentClip;
                EditorGUILayout.LabelField("当前音频信息：", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"名称：{clip.name}");
                EditorGUILayout.LabelField($"长度：{clip.length:F2}秒");
                EditorGUILayout.LabelField($"采样率：{clip.frequency}Hz");
                EditorGUILayout.LabelField($"声道数：{clip.channels}");
                
                // 显示数据信息
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("数据信息：", EditorStyles.boldLabel);
                
                if (dialogManager.manager != null)
                {
                    int animTypeId = dialogManager.manager.animTypeId;
                    int seed = animTypeId * 1024 + 3000;
                    EditorGUILayout.LabelField($"AnimTypeId：{animTypeId}");
                    EditorGUILayout.LabelField($"Seed：{seed}");
                    
                    if (dialogManager.manager.avatarRoot?.avatarData != null)
                    {
                        EditorGUILayout.LabelField($"AvatarId：{dialogManager.manager.avatarRoot.avatarData.name}");
                    }
                    else
                    {
                        EditorGUILayout.LabelField("AvatarId：无法获取");
                    }
                }
                else
                {
                    EditorGUILayout.LabelField("Manager：未初始化");
                }
                
                if (dialogManager.currSentences != null)
                {
                    EditorGUILayout.LabelField($"对话数据：{dialogManager.currSentences.Count} 条");
                }
                else
                {
                    EditorGUILayout.LabelField("对话数据：无");
                }
            }
            else
            {
                EditorGUILayout.HelpBox("未找到DialogManager组件或currentClip为空", MessageType.Warning);
            }
            
            EditorGUILayout.Space();
            
            // 导出按钮
            GUI.enabled = dialogManager != null && dialogManager.currentClip != null;
            if (GUILayout.Button("导出AudioClip和数据文件", GUILayout.Height(30)))
            {
                exporter.ExportAudioClip();
            }
            GUI.enabled = true;
            
            // 信息按钮
            EditorGUILayout.Space();
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("显示音频信息"))
            {
                exporter.LogAudioInfo();
            }
            
            if (GUILayout.Button("显示数据信息"))
            {
                exporter.LogDataInfo();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            EditorGUILayout.HelpBox("点击按钮将导出：\n1. WAV音频文件\n2. JSON数据文件（包含seed、path、dialogData、avatarId、animTypeId）", MessageType.Info);
        }
    }
}
#endif 