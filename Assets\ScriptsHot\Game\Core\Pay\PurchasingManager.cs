using System;
using System.Collections.Generic;
using Msg.basic;
using Msg.economic;
using ScriptsHot.Game.Modules.Shop;
using UnityEngine;
using UnityEngine.Purchasing;
using UnityEngine.Purchasing.Extension;

namespace Game
{


    public class PurchasingManager : MonoSingleton<PurchasingManager>, IDetailedStoreListener
    {
       


        private Action _success;
        private Action<string, bool> _fail;
        private Action _onPending;
        private Action _onComplete;
        private bool _showError;
        IStoreController m_StoreController;
#if UNITY_IOS
        IAppleExtensions m_AppleExtensions;
#elif UNITY_ANDROID
        IGooglePlayStoreExtensions m_GooglePlayExtensions;
#endif

        //subscriptionInfoDic key - product id
        /// <summary>
        /// 订阅信息
        /// </summary>
        /// <param name="subscriptionInfoDic"></param>
        /// <param name="InAppPurchaseInfoDic"></param>
        /// <param name="specialProductId"></param> 2025/07/14 添加特殊商品，服务器传的类型是PB_SubscriptionInfo ， 实际类型是ProductType.Consumable
        public void InitializePurchasing(Dictionary<string, PB_SubscriptionInfo> subscriptionInfoDic, 
            Dictionary<string, PB_InAppPurchaseInfo> InAppPurchaseInfoDic
            ,PB_SubscriptionInfo specialProduct)
        {
            if (m_StoreController != null) return;
            
            Main.Log("PurchasingManager -- InitializePurchasing -- start");
            MsgManager.instance.RegisterCallBack<SC_VerifyReceiptDataResp>(VRD_Callback);
            //StandardPurchasingModule.Instance().useFakeStoreUIMode = FakeStoreUIMode.DeveloperUser;
            var builder = ConfigurationBuilder.Instance(StandardPurchasingModule.Instance());

            Main.Log("PurchasingManager -- InitializePurchasing -- addItem");

            //这里把所有商品注册一下
            AppendShopItems(builder, InAppPurchaseInfoDic);
            AppendMemberItems(builder, subscriptionInfoDic);

            if (specialProduct != null)
            {
                Main.Log("Start Initialize Product ID: " + specialProduct.product_id);
                builder.AddProduct(specialProduct.product_id, ProductType.Consumable);
            }
            

            Main.Log("UnityPurchasing.Initialize(this, builder)");
            UnityPurchasing.Initialize(this, builder);
        }

        private void AppendShopItems(ConfigurationBuilder builder, Dictionary<string, PB_InAppPurchaseInfo> InAppPurchaseInfoDic)
        {
            var rkStr = AppRegionInfo.GetCurrRegionKeyAsStr();
            Main.Log("Current RegionKey ="+rkStr);
            
         
            foreach (var item in InAppPurchaseInfoDic)
            {
                Main.Log("Start Initialize Product ID: " + item.Value.product_id);
                builder.AddProduct(item.Value.product_id, ProductType.Consumable);
            }
            
        }

        //subscribe type   
        private void AppendMemberItems(ConfigurationBuilder builder, Dictionary<string, PB_SubscriptionInfo> subscriptionInfoDic)
        {
            var rkStr = AppRegionInfo.GetCurrRegionKeyAsStr();
           
            foreach (var item in subscriptionInfoDic) //key - product id
            {
                builder.AddProduct(item.Key, ProductType.Subscription);
            }
        }

        public void OnInitialized(IStoreController controller, IExtensionProvider extensions)
        {
            Main.Log("支付模块初始化成功");
            m_StoreController = controller;
#if UNITY_IOS
            m_AppleExtensions = extensions.GetExtension<IAppleExtensions>();
#elif UNITY_ANDROID
            m_GooglePlayExtensions = extensions.GetExtension<IGooglePlayStoreExtensions>();
#endif
            

        }

        public void OnInitializeFailed(InitializationFailureReason error)
        {
            Main.Log("Purchasing Initialize Failed" + error);
            return;
        }

        public void OnInitializeFailed(InitializationFailureReason error, string message)
        {
            Main.Log("Purchasing Initialize Failed" + error + message);
            return;
        }

        public void OnPurchaseFailed(Product product, PurchaseFailureDescription failureDescription)
        {
            var reason = LitJson.JsonMapper.ToJson(failureDescription);
            Main.Log("Purchase Failed" + reason);
            if (cur_orderID == null)
            {
                VFDebug.LogError($"OnPurchaseFailed cur_orderID = {cur_orderID}");
            }
            MsgManager.instance.SendMsg(new CS_CancelOrderReq() { order_id = cur_orderID, reason = reason });

            // 使用新的支付失败处理器显示弹窗
            bool showError = failureDescription.reason != PurchaseFailureReason.UserCancelled;
            PaymentFailureHandler.HandlePaymentFailure(failureDescription, cur_productID, showError);

            _fail?.Invoke(failureDescription.message, false);
            _fail = null;
            purchasing_lock = false;
            return;
        }

        public void OnPurchaseFailed(Product product, PurchaseFailureReason failureReason)
        {
            Main.Log("Purchase Failed" + failureReason);
            if (cur_orderID == null)
            {
                VFDebug.LogError($"OnPurchaseFailed cur_orderID = {cur_orderID}");
            }

            MsgManager.instance.SendMsg(new CS_CancelOrderReq() { order_id = cur_orderID, reason = failureReason + "" });

            // 使用新的支付失败处理器显示弹窗
            bool showError = failureReason != PurchaseFailureReason.UserCancelled;
            PaymentFailureHandler.HandlePaymentFailure(failureReason, failureReason.ToString(), cur_productID, showError);

            _fail?.Invoke(failureReason.ToString(), false);
            _fail = null;
            purchasing_lock = false;
            return;
        }


        public struct PriceInfo
        {
            public string Description;
            public decimal Price;

        }
        /// <summary>
        /// 获得一个Product的原始价格
        /// </summary>
        /// <param name="productId"></param>
        public PriceInfo GetPrice(string productId)
        {
            var product = m_StoreController.products.WithID(productId);      
            return new PriceInfo(){  Description = product.metadata.localizedDescription, Price = product.metadata.localizedPrice};
        }

        /// <summary>
        /// 获取单个已经订阅的订阅的订阅信息
        /// </summary>
        /// <param name="productId"></param>
        /// <returns></returns>
        public SubscriptionInfo GetSubscriptionInfo(string productId)
        {
            var subscription = m_StoreController.products.WithID(productId);
            if (subscription.receipt == null)
            {
                return null;
            }
            var subscriptionManager = new SubscriptionManager(subscription, null);
            return subscriptionManager.getSubscriptionInfo();
        }


        public PriceInfo GetSubPrice(string productId, string curSubProductId = "")
        {
            Debug.LogWarning("HSW productId:"+productId);
            Debug.LogWarning("HSW curSubProductId:"+curSubProductId);
            var toBuyPrice = GetPrice(productId);
            if (!string.IsNullOrEmpty(curSubProductId))
            {
                var curSubInfo = GetSubscriptionInfo(curSubProductId);
                int last_days = (curSubInfo.getExpireDate() - DateTime.Today).Days;//剩余的日子
                if ((float)curSubInfo.getSubscriptionPeriod().Days == 0)
                {
                    return new PriceInfo() { Description = toBuyPrice.Description, Price = toBuyPrice.Price };
                }
                float last_rate = (float)last_days / (float)curSubInfo.getSubscriptionPeriod().Days;
                var curProduct = m_StoreController.products.WithID(productId);
                Debug.LogWarning("HSW last_days:"+last_days);
                Debug.LogWarning("HSW last_rate:"+last_rate);
                return new PriceInfo() { Description = toBuyPrice.Description, Price = toBuyPrice.Price - curProduct.metadata.localizedPrice * (decimal)last_rate};
            }
            else
            {
                //当前没有订阅直接返回新的价格
                return toBuyPrice;
            }
        }
        private struct Data
        {
            public string transactionID;
            public string receipt;
            public string appleOriginalTransactionID;
            public bool hasReceipt;
            public bool availableToPurchase;
            public bool appleProductIsRestored;
        }

        public PurchaseProcessingResult ProcessPurchase(PurchaseEventArgs purchaseEvent)
        {
            if (string.IsNullOrEmpty(cur_orderID))
            {
                //标记为丢单处理逻辑
                return PurchaseProcessingResult.Pending;
            }
            Debug.Log(purchaseEvent);
            Main.Log("===================");
            Main.Log(purchaseEvent.purchasedProduct.appleOriginalTransactionID);            
            Main.Log(purchaseEvent.purchasedProduct.appleProductIsRestored.ToString());
            Main.Log(purchaseEvent.purchasedProduct.availableToPurchase.ToString()); 
            Main.Log(purchaseEvent.purchasedProduct.hasReceipt.ToString());         
            Main.Log(purchaseEvent.purchasedProduct.receipt);
            Main.Log("==================="); 
            if (StringComparer.Ordinal.Equals(purchaseEvent.purchasedProduct.definition.id, cur_productID))
            {
                VFDebug.Log($"前端已收到购买商品{cur_productID}完成！订单号:{cur_orderID}");
                VFDebug.Log($"前端开始向后端申请订单校验");
                var cs_msg = new CS_VerifyReceiptDataReq()
                {
                    apple_original_transaction_id = string.IsNullOrEmpty(purchaseEvent.purchasedProduct.appleOriginalTransactionID)
                        ? ""
                        : purchaseEvent.purchasedProduct.appleOriginalTransactionID,
                    apple_product_is_restored = purchaseEvent.purchasedProduct.appleProductIsRestored,
                    available_to_purchase = purchaseEvent.purchasedProduct.availableToPurchase,
                    has_receipt = purchaseEvent.purchasedProduct.hasReceipt,
                    order_id = cur_orderID,
                    product_type = cur_product_type,
                    receipt = purchaseEvent.purchasedProduct.receipt,
                    transaction_id = purchaseEvent.purchasedProduct.definition.id,
                    user_id = PushManager.instance.GetUserId(),
#if UNITY_IOS
                    economic_source = PB_EconomicSourceEnum.APPLE
#elif UNITY_ANDROID
                    economic_source = PB_EconomicSourceEnum.GOOGLE
#endif
                };
                MsgManager.instance.SendMsg(cs_msg);
                _onComplete?.Invoke();
            }
            else
            {
                VFDebug.Log($"前端已收到购买商品{cur_productID}的订单正在处理中！订单号:{cur_orderID}");
                _onPending?.Invoke();
            }
            return PurchaseProcessingResult.Pending;
        }

        private void VRD_Callback(SC_VerifyReceiptDataResp msg)
        {
            Main.Log($"SC_VerifyReceiptDataResp   msg = {msg.status_code}");
            if (msg.status_code == 200)
            {
                bool isRedemptionCodeScenario = string.IsNullOrEmpty(cur_orderID);

                if (isRedemptionCodeScenario)
                {
                    VFDebug.Log($"兑换码订阅验证成功: {cur_productID}");
                }
                else
                {
                    VFDebug.Log($"商品号为{cur_productID}的订单{cur_orderID}成功通过服务器校验!");
                }

                var product = m_StoreController.products.WithID(cur_productID);
                if (product != null && product.metadata != null)
                {
                    if (cur_product_type == PB_ProductType.PB_ProductType_Diamond)
                    {
                        AFHelper.af_purchase(cur_productID, product.metadata.localizedPriceString , product.metadata.isoCurrencyCode);
                    }
                    else if (cur_product_type == PB_ProductType.PB_ProductType_Subscribe)
                    {
                        VFDebug.Log($"订阅验证成功");

                        // 只有非兑换码场景才发送AF事件，避免重复统计
                        if (!isRedemptionCodeScenario)
                        {
                            AFHelper.af_subscribe(cur_productID, product.metadata.localizedPriceString , product.metadata.isoCurrencyCode);
                            AFHelper.af_start_trial();
                            FacebookHelper.EVENT_NAME_START_TRIAL();
                        }
                    }
                }

                // 如果有当前产品ID，确认购买
                if (!string.IsNullOrEmpty(cur_productID))
                {
                    ConfirmPendingPurchase(cur_productID);
                }

                purchasing_lock = false;
                _success?.Invoke();

                // 订阅验证成功后，通知刷新会员状态
                if (cur_product_type == PB_ProductType.PB_ProductType_Subscribe)
                {
                    VFDebug.Log("订阅验证成功，通知刷新会员状态");
                    // 延迟刷新，确保服务器状态已更新
                    TimerManager.instance.RegisterTimer((c) =>
                    {
                        var homepageController = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage);
                        homepageController?.ReqGetIncentiveData();
                    }, 1500, 1);
                }
            }
            else
            {
                bool isRedemptionCodeScenario = string.IsNullOrEmpty(cur_orderID);
                if (isRedemptionCodeScenario)
                {
                    Debug.LogError($"兑换码订阅验证失败: {cur_productID}, error: {msg.status_code}");
                }
                else
                {
                    Debug.LogError($"商品号为{cur_productID}的订单{cur_orderID}未能通过服务器校验! +error:"+msg.status_code);
                }

                purchasing_lock = false;
                _fail?.Invoke($"验证失败: {msg.status_code}", false);
            }
        }

        public void ClearLockIPA()
        {
            foreach (var product in m_StoreController.products.all)
            {
                m_StoreController.ConfirmPendingPurchase(product);
            }
        }
        
        /// <summary>
        /// 当服务器履行完购买后，根据回调调用这个
        /// </summary>
        /// <param name="productId"></param>
        public void ConfirmPendingPurchase(string productId)
        {
            var product = m_StoreController.products.WithID(productId);
            m_StoreController.ConfirmPendingPurchase(product);
        }

        /// <summary>
        /// 检测一个订阅是否已经订阅
        /// </summary>
        /// <param name="productId"></param>
        /// <returns></returns>
        public bool IsSubscribedTo(string productId)
        {
            var subscription = m_StoreController.products.WithID(productId);
            if (subscription.receipt == null)
            {
                return false;
            }
            var subscriptionManager = new SubscriptionManager(subscription, null);
            var info = subscriptionManager.getSubscriptionInfo();
            return info.isSubscribed() == Result.True;
        }

        /// <summary>
        /// 检查所有订阅产品的状态，用于处理兑换码等场景
        /// </summary>
        /// <param name="callback">回调函数，参数为是否发现新的有效订阅</param>
        public void CheckAllSubscriptionStatus(System.Action<bool> callback = null)
        {
            if (m_StoreController == null)
            {
                VFDebug.LogWarning("StoreController未初始化，无法检查订阅状态");
                callback?.Invoke(false);
                return;
            }

            bool foundActiveSubscription = false;
            var products = m_StoreController.products.all;

            foreach (var product in products)
            {
                if (product.definition.type == ProductType.Subscription && product.hasReceipt)
                {
                    try
                    {
                        var subscriptionManager = new SubscriptionManager(product, null);
                        var info = subscriptionManager.getSubscriptionInfo();

                        if (info.isSubscribed() == Result.True)
                        {
                            VFDebug.Log($"发现有效订阅: {product.definition.id}");
                            foundActiveSubscription = true;

                            // 向服务器验证这个订阅
                            VerifySubscriptionReceipt(product);
                        }
                    }
                    catch (System.Exception e)
                    {
                        VFDebug.LogError($"检查订阅状态时出错: {product.definition.id}, 错误: {e.Message}");
                    }
                }
            }

            callback?.Invoke(foundActiveSubscription);
        }

        /// <summary>
        /// 验证订阅收据
        /// </summary>
        /// <param name="product"></param>
        private void VerifySubscriptionReceipt(Product product)
        {
            if (product == null || !product.hasReceipt)
            {
                VFDebug.LogWarning($"产品 {product?.definition.id} 没有收据，无法验证");
                return;
            }

            VFDebug.Log($"开始验证订阅收据: {product.definition.id}");

            // 临时保存当前产品信息，用于兑换码场景
            var originalProductID = cur_productID;
            var originalProductType = cur_product_type;

            // 设置当前验证的产品信息
            cur_productID = product.definition.id;
            cur_product_type = PB_ProductType.PB_ProductType_Subscribe;

            var cs_msg = new CS_VerifyReceiptDataReq()
            {
                apple_original_transaction_id = product.appleOriginalTransactionID,
                apple_product_is_restored = product.appleProductIsRestored,
                available_to_purchase = product.availableToPurchase,
                has_receipt = product.hasReceipt,
                order_id = "", // 兑换码场景下可能没有订单ID
                product_type = product.definition.type == ProductType.Consumable? PB_ProductType.PB_ProductType_Diamond : PB_ProductType.PB_ProductType_Subscribe,
                // product_type = PB_ProductType.PB_ProductType_Subscribe,                
                receipt = product.receipt,
                transaction_id = product.definition.id,
                // transaction_id = product.transactionID,
                user_id = PushManager.instance.GetUserId(),
                economic_source = PB_EconomicSourceEnum.APPLE
            };
            
            
            MsgManager.instance.SendMsg(cs_msg);
            VFDebug.Log($"已发送兑换码订阅验证请求: {product.definition.id}");
        }

        /// <summary>
        /// 恢复购买
        /// </summary>
        public void Restore(Action<bool,string> callback)
        {
#if UNITY_IOS
            m_AppleExtensions.RestoreTransactions(callback);
#elif UNITY_ANDROID
            m_GooglePlayExtensions.RestoreTransactions(callback);
#endif
        }


        private PB_ProductType cur_product_type;
        private string cur_orderID;
        private string cur_productID;
        private bool purchasing_lock = false;

        /// <summary>
        /// 检查网络连接状态
        /// </summary>
        /// <returns>网络是否连接正常</returns>
        private bool CheckNetworkConnection()
        {
            // 检查网络连接状态
            if (Application.internetReachability == NetworkReachability.NotReachable)
            {
                VFDebug.LogWarning("网络连接不可用，无法进行支付");
                return false;
            }
            return true;
        }

        public async void StartPurchasing(string productID, PB_ProductType product_type, Action success, Action<string, bool> fail, Action onComplete = null, Action onPending = null)
        {
            // 检查网络连接
            if (!CheckNetworkConnection())
            {
                // 网络连接不可用，显示网络错误弹窗
                PaymentFailureHandler.HandlePaymentFailure(
                    PurchaseFailureReason.Unknown,
                    "Network connection unavailable",
                    productID,
                    true
                );
                fail?.Invoke(I18N.inst.MoStr("ui_pay_error01"), false);
                return;
            }

            if (purchasing_lock) return;
            purchasing_lock = true;
            try
            {
                var guid = Guid.NewGuid().ToString();
#if UNITY_IOS
                m_AppleExtensions.SetApplicationUsername(guid);
#endif

                VFDebug.Log("StartPurchasing  guid = " + guid);
                Main.Log($"productID = {productID} product_type = {product_type}");
                long timeStamp = DateTimeOffset.Now.ToUnixTimeSeconds();
                Main.Log("StartPurchasing  productID=" + productID);
                var res = await MsgManager.instance.SendAsyncMsg<SC_CreateOrderAck>(new CS_CreateOrderReq() { product_id = productID, product_type = product_type, req_id = PushManager.instance.GetUserId()+timeStamp.ToString(), trade_id = guid});
                if (res == null ) {
                    Main.Log("StartPurchasing1 res= null");
                }
                if (res.data == null)
                {
                    Main.Log("StartPurchasing2 res.data= null");
                }
                if (res.data.order_id == null)
                {
                    Main.Log("StartPurchasing3 res.data= null");
                }
                if (m_StoreController == null)
                {
                    Main.Log("StartPurchasing4 m_StoreController= null");
                }

                Main.Log($"productID = {productID}");
                Main.Log($"order_id = {res.data.order_id}");
                if (string.IsNullOrEmpty(res.data.order_id))
                {
                    VFDebug.LogError($"SC_CreateOrderAck 返回个order_id为空的订单，商品id= {productID} timeStamp = {timeStamp}");
                }


                if (res.code != Msg.basic.PB_BizCode.PB_BizCode_Success)
                {
                    fail(res.code.ToString(), true);
                    return;
                }
                cur_productID = productID;
                cur_product_type = product_type;
                _success = success;
                _fail = fail;
                _onPending = onPending;
                _onComplete = onComplete;
                cur_orderID = res.data.order_id;
                Main.Log($"m_StoreController.InitiatePurchase productID = {productID} cur_orderID = {cur_orderID}");
                m_StoreController.InitiatePurchase(productID, cur_orderID);
            }
            catch (GRpcException ex)
            {
                Main.Log(ex.Message);
                fail(ex.ToString(), true);
                purchasing_lock = false;
                return;
            }
        }
    }
}