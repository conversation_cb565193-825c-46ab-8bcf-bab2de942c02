using FairyGUI;
using UIBind.WhatsApp;
using UnityEngine;

namespace Game.Modules.WhatsApp
{
    /// <summary>
    /// WhatsApp 引导界面 UI
    /// 用于引导用户使用 WhatsApp 相关功能
    /// </summary>
    public class WhatsappGuiderUI : BaseUI<WhatsappGuiderPanel>
    {
        public WhatsappGuiderUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top;
        
        protected override bool isFullScreen => true;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            
            // 注册按钮点击事件
            AddUIEvent(ui.maskBtn.onClick, OnMaskBtnClick);
            AddUIEvent(ui.closeBtn.onClick, OnCloseBtnClick);
            AddUIEvent(ui.closeBtn2.onClick, OnCloseBtnClick);
            AddUIEvent(ui.btn1.onClick, OnBtn1Click);
            
            // 初始化文本内容
            InitializeTexts();
        }

        protected override void OnShow()
        {
            base.OnShow();
            
            GetController<WhatsappController>(ModelConsts.Whatsapp).SendBI_click_incentive_whatsapp_icon();
            GetController<WhatsappController>(ModelConsts.Whatsapp).SendBI_appear_incentive_whatsapp_join_popup();  
            // 显示时的逻辑
            VFDebug.Log("WhatsappGuiderUI 显示");
        }

        protected override void OnHide()
        {
            base.OnHide();
            
            // 隐藏时的逻辑
            VFDebug.Log("WhatsappGuiderUI 隐藏");
        }

        /// <summary>
        /// 初始化文本内容
        /// </summary>
        private void InitializeTexts()
        {
            // 设置国际化文本键
            ui.content1.SetKey("ui_onboard_whatsapp_join_title");
            ui.content2.SetKey("ui_onboard_whatsapp_join_desc");
            ui.content3.SetKey("ui_onboard_whatsapp_instruction");
            ui.btn1.SetKey("ui_onboard_whatsapp_button");
        }

        /// <summary>
        /// 蒙版按钮点击事件
        /// </summary>
        private void OnMaskBtnClick()
        {
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void OnCloseBtnClick()
        {
            Hide();
        }

        /// <summary>
        /// 主要操作按钮点击事件
        /// </summary>
        private void OnBtn1Click()
        {
            GetController<WhatsappController>(ModelConsts.Whatsapp).SendBI_click_incentive_whatsapp_join_popup_reminder_button();
            Application.OpenURL(GetController<WhatsappController>(ModelConsts.Whatsapp).WhatsappUrl);
            // 关闭引导界面
            Hide();
        }

        #region 备用
// /// <summary>
//         /// 处理 WhatsApp 相关操作
//         /// </summary>
//         private void HandleWhatsAppAction()
//         {
//             try
//             {
//                 // 检查 WhatsApp 是否已安装
//                 bool isInstalled = WhatsAppHelper.IsWhatsAppInstalled();
//                 
//                 if (isInstalled)
//                 {
//                     // 如果已安装，打开 WhatsApp
//                     WhatsAppHelper.OpenWhatsApp();
//                     
//                     // 显示成功提示
//                     ShowToast("正在打开 WhatsApp...");
//                 }
//                 else
//                 {
//                     // 如果未安装，引导用户下载
//                     ShowConfirmDialog(
//                         "WhatsApp 未安装",
//                         "检测到您的设备未安装 WhatsApp，是否前往下载？",
//                         () => {
//                             WhatsAppHelper.OpenWhatsAppDownloadPage();
//                             ShowToast("正在跳转到下载页面...");
//                         },
//                         () => {
//                             VFDebug.Log("用户取消下载 WhatsApp");
//                         }
//                     );
//                 }
//             }
//             catch (System.Exception ex)
//             {
//                 VFDebug.LogError($"WhatsApp 操作失败: {ex.Message}");
//                 ShowToast("操作失败，请稍后重试");
//             }
//         }
//
//         /// <summary>
//         /// 显示 Toast 提示
//         /// </summary>
//         /// <param name="message">提示消息</param>
//         private void ShowToast(string message)
//         {
//             var toastUI = UIManager.instance.GetUI<CommonToastUI>(UIConsts.CommonToast);
//             if (toastUI != null)
//             {
//                 toastUI.ShowToast(message, true);
//             }
//         }
//
//         /// <summary>
//         /// 显示确认对话框
//         /// </summary>
//         /// <param name="title">标题</param>
//         /// <param name="content">内容</param>
//         /// <param name="confirmAction">确认回调</param>
//         /// <param name="cancelAction">取消回调</param>
//         private void ShowConfirmDialog(string title, string content, System.Action confirmAction, System.Action cancelAction)
//         {
//             var confirmUI = UIManager.instance.GetUI<CommConfirmUI>(UIConsts.CommConfirm);
//             if (confirmUI != null)
//             {
//                 confirmUI.Open(
//                     content: content,
//                     confirmFunc: confirmAction,
//                     cancelFunc: cancelAction,
//                     type: 0, // 双按钮
//                     confirmLabel: "确定",
//                     cancelLabel: "取消",
//                     block: false,
//                     iconType: 2 // 询问图标
//                 );
//             }
//         }
//
//         /// <summary>
//         /// 显示 WhatsApp 群聊引导
//         /// </summary>
//         public void ShowGroupChatGuide()
//         {
//             SetTexts(
//                 "加入 WhatsApp 学习群",
//                 "与其他学习者一起交流，分享学习心得和经验。",
//                 "点击下方按钮加入我们的学习群组。",
//                 "加入群组"
//             );
//             
//             // 重新绑定按钮事件为群聊操作
//             RemoveUIEvent(ui.btn1.onClick, OnBtn1Click);
//             AddUIEvent(ui.btn1.onClick, OnJoinGroupClick);
//         }
//
//         /// <summary>
//         /// 加入群组按钮点击事件
//         /// </summary>
//         private void OnJoinGroupClick()
//         {
//             VFDebug.Log("WhatsappGuiderUI: 点击加入群组按钮");
//             
//             try
//             {
//                 // 打开 WhatsApp 群聊
//                 WhatsAppHelper.OpenWhatsAppGroupChat();
//                 ShowToast("正在打开 WhatsApp 群聊...");
//             }
//             catch (System.Exception ex)
//             {
//                 VFDebug.LogError($"打开 WhatsApp 群聊失败: {ex.Message}");
//                 ShowToast("打开群聊失败，请稍后重试");
//             }
//             
//             Hide();
//         }
//
//         /// <summary>
//         /// 显示分享引导
//         /// </summary>
//         public void ShowShareGuide()
//         {
//             SetTexts(
//                 "分享到 WhatsApp",
//                 "将您的学习成果分享给朋友，一起进步！",
//                 "点击下方按钮分享应用给朋友。",
//                 "立即分享"
//             );
//             
//             // 重新绑定按钮事件为分享操作
//             RemoveUIEvent(ui.btn1.onClick, OnBtn1Click);
//             AddUIEvent(ui.btn1.onClick, OnShareClick);
//         }
//
//         /// <summary>
//         /// 分享按钮点击事件
//         /// </summary>
//         private void OnShareClick()
//         {
//             VFDebug.Log("WhatsappGuiderUI: 点击分享按钮");
//             
//             try
//             {
//                 // 分享应用到 WhatsApp
//                 WhatsAppHelper.ShareAppToWhatsApp("快来试试这个很棒的语言学习应用！");
//                 ShowToast("正在打开 WhatsApp 分享...");
//             }
//             catch (System.Exception ex)
//             {
//                 VFDebug.LogError($"分享到 WhatsApp 失败: {ex.Message}");
//                 ShowToast("分享失败，请稍后重试");
//             }
//             
//             Hide();
//         }
        #endregion
        
    }
}
