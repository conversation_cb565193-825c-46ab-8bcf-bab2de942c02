using System;
using UnityEngine;

namespace Game.Modules.WhatsApp
{
    /// <summary>
    /// WhatsApp 功能管理器
    /// 统一管理 WhatsApp 相关的 UI 和功能
    /// </summary>
    public class WhatsAppManager : MonoSingleton<WhatsAppManager>
    {
        /// <summary>
        /// 显示 WhatsApp 引导界面
        /// </summary>
        /// <param name="guideType">引导类型</param>
        public void ShowGuider(WhatsAppGuideType guideType = WhatsAppGuideType.Default)
        {
            var guiderUI = UIManager.instance.GetUI<WhatsappGuiderUI>(UIConsts.WhatsappGuider);
            
            if (guiderUI == null)
            {
                VFDebug.LogError("WhatsappGuiderUI 未找到，请检查 UIConsts 配置");
                return;
            }
            
            // 根据引导类型设置不同的内容
            switch (guideType)
            {
                case WhatsAppGuideType.Default:
                    // 使用默认配置
                    break;
                    
                case WhatsAppGuideType.GroupChat:
                    // guiderUI.ShowGroupChatGuide();
                    break;
                    
                case WhatsAppGuideType.Share:
                    // guiderUI.ShowShareGuide();
                    break;
            }
            
            guiderUI.Show();
        }


        #region 备用
// /// <summary>
        // /// 快速打开 WhatsApp
        // /// </summary>
        // public void QuickOpenWhatsApp()
        // {
        //     try
        //     {
        //         bool isInstalled = WhatsAppHelper.IsWhatsAppInstalled();
        //         
        //         if (isInstalled)
        //         {
        //             WhatsAppHelper.OpenWhatsApp();
        //             ShowToast("正在打开 WhatsApp...");
        //         }
        //         else
        //         {
        //             // 显示引导界面
        //             ShowGuider(WhatsAppGuideType.Default);
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"打开 WhatsApp 失败: {ex.Message}");
        //         ShowToast("打开 WhatsApp 失败");
        //     }
        // }
        //
        // /// <summary>
        // /// 快速打开 WhatsApp 群聊
        // /// </summary>
        // /// <param name="groupId">群聊 ID，为空则使用默认群聊</param>
        // public void QuickOpenGroupChat(string groupId = null)
        // {
        //     try
        //     {
        //         bool isInstalled = WhatsAppHelper.IsWhatsAppInstalled();
        //         
        //         if (isInstalled)
        //         {
        //             WhatsAppHelper.OpenWhatsAppGroupChat(groupId);
        //             ShowToast("正在打开 WhatsApp 群聊...");
        //         }
        //         else
        //         {
        //             // 显示群聊引导界面
        //             ShowGuider(WhatsAppGuideType.GroupChat);
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"打开 WhatsApp 群聊失败: {ex.Message}");
        //         ShowToast("打开群聊失败");
        //     }
        // }
        //
        // /// <summary>
        // /// 快速分享到 WhatsApp
        // /// </summary>
        // /// <param name="message">分享消息</param>
        // public void QuickShare(string message = null)
        // {
        //     try
        //     {
        //         bool isInstalled = WhatsAppHelper.IsWhatsAppInstalled();
        //         
        //         if (isInstalled)
        //         {
        //             if (string.IsNullOrEmpty(message))
        //             {
        //                 WhatsAppHelper.ShareAppToWhatsApp();
        //             }
        //             else
        //             {
        //                 WhatsAppHelper.ShareAppToWhatsApp(message);
        //             }
        //             ShowToast("正在打开 WhatsApp 分享...");
        //         }
        //         else
        //         {
        //             // 显示分享引导界面
        //             ShowGuider(WhatsAppGuideType.Share);
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"分享到 WhatsApp 失败: {ex.Message}");
        //         ShowToast("分享失败");
        //     }
        // }
        //
        // /// <summary>
        // /// 发送消息到指定联系人
        // /// </summary>
        // /// <param name="phoneNumber">电话号码</param>
        // /// <param name="message">消息内容</param>
        // public void SendMessage(string phoneNumber, string message = "")
        // {
        //     try
        //     {
        //         bool isInstalled = WhatsAppHelper.IsWhatsAppInstalled();
        //         
        //         if (isInstalled)
        //         {
        //             WhatsAppHelper.SendWhatsAppMessage(phoneNumber, message);
        //             ShowToast("正在打开 WhatsApp 发送消息...");
        //         }
        //         else
        //         {
        //             ShowGuider(WhatsAppGuideType.Default);
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"发送 WhatsApp 消息失败: {ex.Message}");
        //         ShowToast("发送消息失败");
        //     }
        // }
        //
        // /// <summary>
        // /// 检查 WhatsApp 安装状态并显示结果
        // /// </summary>
        // public void CheckInstallationStatus()
        // {
        //     try
        //     {
        //         bool isInstalled = WhatsAppHelper.IsWhatsAppInstalled();
        //         string message = isInstalled ? "WhatsApp 已安装" : "WhatsApp 未安装";
        //         
        //         ShowConfirmDialog(
        //             "WhatsApp 状态",
        //             message,
        //             () => {
        //                 if (!isInstalled)
        //                 {
        //                     WhatsAppHelper.OpenWhatsAppDownloadPage();
        //                 }
        //             },
        //             null,
        //             isInstalled ? "确定" : "去下载",
        //             "取消"
        //         );
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"检查 WhatsApp 安装状态失败: {ex.Message}");
        //         ShowToast("检查失败");
        //     }
        // }
        //
        // /// <summary>
        // /// 设置默认群聊 ID
        // /// </summary>
        // /// <param name="groupId">群聊邀请码</param>
        // public void SetDefaultGroupId(string groupId)
        // {
        //     try
        //     {
        //         WhatsAppHelper.SetDefaultGroupChatId(groupId);
        //         ShowToast($"已设置默认群聊: {groupId}");
        //         VFDebug.Log($"设置默认 WhatsApp 群聊 ID: {groupId}");
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"设置默认群聊 ID 失败: {ex.Message}");
        //         ShowToast("设置失败");
        //     }
        // }
        //
        // /// <summary>
        // /// 获取当前默认群聊 ID
        // /// </summary>
        // /// <returns>默认群聊 ID</returns>
        // public string GetDefaultGroupId()
        // {
        //     try
        //     {
        //         return WhatsAppHelper.GetDefaultGroupChatId();
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"获取默认群聊 ID 失败: {ex.Message}");
        //         return "";
        //     }
        // }
        //
        // /// <summary>
        // /// 显示 Toast 提示
        // /// </summary>
        // /// <param name="message">提示消息</param>
        // private void ShowToast(string message)
        // {
        //     try
        //     {
        //         var toastUI = UIManager.instance.GetUI<CommonToastUI>(UIConsts.CommonToast);
        //         if (toastUI != null)
        //         {
        //             toastUI.ShowToast(message, true);
        //         }
        //         else
        //         {
        //             VFDebug.Log($"Toast: {message}");
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"显示 Toast 失败: {ex.Message}");
        //     }
        // }
        //
        // /// <summary>
        // /// 显示确认对话框
        // /// </summary>
        // private void ShowConfirmDialog(string title, string content, Action confirmAction, Action cancelAction, string confirmText = "确定", string cancelText = "取消")
        // {
        //     try
        //     {
        //         var confirmUI = UIManager.instance.GetUI<CommConfirmUI>(UIConsts.CommConfirm);
        //         if (confirmUI != null)
        //         {
        //             confirmUI.Open(
        //                 content: content,
        //                 confirmFunc: confirmAction,
        //                 cancelFunc: cancelAction,
        //                 type: cancelAction == null ? 1 : 0, // 单按钮或双按钮
        //                 confirmLabel: confirmText,
        //                 cancelLabel: cancelText,
        //                 block: false,
        //                 iconType: 2 // 询问图标
        //             );
        //         }
        //         else
        //         {
        //             VFDebug.Log($"确认对话框: {content}");
        //             confirmAction?.Invoke();
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"显示确认对话框失败: {ex.Message}");
        //     }
        // }
        //
        // /// <summary>
        // /// 处理 Deep Link 激活事件
        // /// </summary>
        // /// <param name="url">Deep Link URL</param>
        // public void HandleDeepLink(string url)
        // {
        //     try
        //     {
        //         VFDebug.Log($"WhatsAppManager 处理 Deep Link: {url}");
        //         
        //         // 这里可以根据 Deep Link 的内容执行相应的操作
        //         // 比如打开特定的功能或显示特定的引导
        //         
        //         if (url.Contains("whatsapp"))
        //         {
        //             ShowToast("欢迎从 WhatsApp 访问我们的应用！");
        //             // 可以显示特殊的欢迎界面或给予奖励
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         VFDebug.LogError($"处理 Deep Link 失败: {ex.Message}");
        //     }
        // }
        #endregion
        
    }

    /// <summary>
    /// WhatsApp 引导类型
    /// </summary>
    public enum WhatsAppGuideType
    {
        /// <summary>
        /// 默认引导
        /// </summary>
        Default,
        
        /// <summary>
        /// 群聊引导
        /// </summary>
        GroupChat,
        
        /// <summary>
        /// 分享引导
        /// </summary>
        Share
    }
}
