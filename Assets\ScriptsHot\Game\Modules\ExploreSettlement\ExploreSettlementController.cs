﻿using Msg.explore;
using Newtonsoft.Json;

namespace ScriptsHot.Game.Modules.ExploreSettlement
{
    public class ExploreSettlementController : BaseController
    {
        public ExploreSettlementController() : base(ModelConsts.ExploreSettlement)
        {
        }

        public ExploreSettlementModel Model => GetModel<ExploreSettlementModel>(ModelConsts.ExploreSettlement);

        public override void OnInit()
        {
            RegisterModel(new ExploreSettlementModel());
            RegisterUI(new ExploreSettlementUI(UIConsts.ExploreSettlementUI));
            
            // 对话结算
            MsgManager.instance.RegisterCallBack<SC_MissionStoryChatDownMsgForSettlement>(this.ShowExploreSettlement);
        }

        public void ShowExploreSettlement(SC_MissionStoryChatDownMsgForSettlement data)
        {
            VFDebug.LogError($"avatar txt 获取结算数据成功！ data:{JsonConvert.SerializeObject(data, Formatting.Indented)}");
            if (data != null)
            {
                Model.SetSettlementInfo(data);
                
                Notifier.instance.SendNotification(NotifyConsts.procedure_explore_chat_end);
           
            }
        }
    }
}