/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreSettlement
{
    public partial class SignPersistDayComponent : UIBindT
    {
        public override string pkgName => "ExploreSettlement";
        public override string comName => "SignPersistDayComponent";

        public Controller state;
        public GImage _;
        public GRichTextField tfDay;
        public GTextField dummy_index;
        public GLoader3D spCheckin;
        public Transition iceHide;
        public Transition iceShow;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);
            _ = (GImage)com.GetChildAt(1);
            tfDay = (GRichTextField)com.GetChildAt(2);
            dummy_index = (GTextField)com.GetChildAt(3);
            spCheckin = (GLoader3D)com.GetChildAt(4);
            iceHide = com.GetTransitionAt(0);
            iceShow = com.GetTransitionAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
            _ = null;
            tfDay = null;
            dummy_index = null;
            spCheckin = null;
            iceHide = null;
            iceShow = null;
        }
    }
}