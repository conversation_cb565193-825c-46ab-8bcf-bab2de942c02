/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class LifeTimePlanItemCom : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "LifeTimePlanItemCom";

        public Controller isSelect;
        public GRichTextField cornerTxt;
        public GGroup selected;
        public GRichTextField tfDesc2;
        public GRichTextField priceNow;
        public GRichTextField pricePre;
        public GGraph selectBtn;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            isSelect = com.GetControllerAt(0);
            cornerTxt = (GRichTextField)com.GetChildAt(2);
            selected = (GGroup)com.GetChildAt(5);
            tfDesc2 = (GRichTextField)com.GetChildAt(6);
            priceNow = (GRichTextField)com.GetChildAt(7);
            pricePre = (GRichTextField)com.GetChildAt(8);
            selectBtn = (GGraph)com.GetChildAt(9);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            isSelect = null;
            cornerTxt = null;
            selected = null;
            tfDesc2 = null;
            priceNow = null;
            pricePre = null;
            selectBtn = null;
        }
    }
}