/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreSettlement
{
    public partial class CompSettleExploreDayItem : UIBindT
    {
        public override string pkgName => "ExploreSettlement";
        public override string comName => "CompSettleExploreDayItem";

        public GRichTextField tfDays;
        public GList listDay;
        public Transition show;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tfDays = (GRichTextField)com.GetChildAt(2);
            listDay = (GList)com.GetChildAt(3);
            show = com.GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tfDays = null;
            listDay = null;
            show = null;
        }
    }
}