#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

namespace ZTest.ZTemp
{
    /// <summary>
    /// GPT批量测试器 - 简单版本
    /// </summary>
    public class GPTBatchTester : MonoBehaviour
    {
        /// <summary>
        /// 测试按钮点击事件
        /// </summary>
        public void TestGPTComponents()
        {
            Debug.Log("开始查找所有GPT组件并发送请求...");
            
            // 查找所有子对象的GPTEditorTool组件
            GPTEditorTool[] gptComponents = transform.GetComponentsInChildren<GPTEditorTool>();
            
            if (gptComponents.Length == 0)
            {
                Debug.Log("未找到任何GPTEditorTool组件");
                return;
            }
            
            Debug.Log($"找到 {gptComponents.Length} 个GPT组件，开始同时发送请求...");
            
            // 同时发送所有请求
            foreach (var gptTool in gptComponents)
            {
                if (gptTool != null && gptTool.isActiveAndEnabled)
                {
                    Debug.Log($"发送请求: {gptTool.gameObject.name}");
                    gptTool.SendToGPT();
                }
            }
            
            Debug.Log($"已同时发送 {gptComponents.Length} 个GPT请求");
        }
    }
}

[CustomEditor(typeof(ZTest.ZTemp.GPTBatchTester))]
public class GPTBatchTesterEditor : Editor
{
    public override void OnInspectorGUI()
    {
        ZTest.ZTemp.GPTBatchTester tester = (ZTest.ZTemp.GPTBatchTester)target;
        
        EditorGUILayout.Space();
        
        // 大按钮
        if (GUILayout.Button("🚀 测试所有GPT组件", GUILayout.Height(40)))
        {
            tester.TestGPTComponents();
        }
        
        EditorGUILayout.Space();
        DrawDefaultInspector();
    }
}
#endif 