using System.Threading.Tasks;
using FairyGUI;
using FairyGUI.Utils;
using UnityEngine;
using YooAsset;

namespace UIBind.common
{
    public partial class LocalImageLoader : ExtendedComponent
    {
        private AssetHandle _handle;
        private string loadingPath;

        public async Task LoadExploreBg(string tag)
        {
            if (string.IsNullOrEmpty(tag))
            {
                VFDebug.LogError("没有背景图 tag");
                return;
            }
            await LoadImage(ResUtils.GetExploreBgImgPath(tag));
            SetAsHeight(this.height);
        }
        public async Task LoadAvatarHead(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                VFDebug.LogError("没有背景图 tag");
                return;
            }
            await LoadImage(ResUtils.GetAvatarHeadPath(name));
        }
        public async Task LoadAvatarBody(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                VFDebug.LogError("没有背景图 tag");
                return;
            }
            await LoadImage(ResUtils.GetAvatarBodyPath(name));
        }

        private async Task LoadImage(string path)
        {
            if (loadingPath == path) return;
            _handle?.Release();

            Debug.Log($"path {path} {loadingPath} #tag");
            _handle = YooAssets.LoadAssetAsync<Texture2D>(path);
            loadingPath = path;

            await _handle.Task;

            if (_handle != null && _handle.Status == EOperationStatus.Succeed)
            {
                Texture2D texture = _handle.AssetObject as Texture2D;
                if (texture != null)
                {
                    // 添加纹理尺寸验证
                    if (texture.width <= 0 || texture.height <= 0 ||
                        texture.width > 16384 || texture.height > 16384)
                    {
                        VFDebug.LogError($"#lll 纹理尺寸异常: {texture.width}x{texture.height}");
                        return;
                    }

                    ldrImage.texture = new NTexture(texture);
                }
            }
            else
            {
                VFDebug.LogError($"LocalImageLoader 图片加载失败: [{path};{_handle?.Status}]");
            }
            _handle?.Release();
            _handle = null;
            loadingPath = null;
        }

        private Vector2 GetOriginalSize()
        {
            if (ldrImage.texture != null)
            {
                return ldrImage.texture.originalSize;
            }
            return Vector2.zero;
        }

        internal void SetAsHeight(float targetHeight)
        {
            var originSize = GetOriginalSize();
            // 添加尺寸验证
            if (originSize.x <= 0 || originSize.y <= 0 || targetHeight <= 0)
            {
                VFDebug.LogError($"#lll 尺寸参数异常: originSize={originSize}, targetHeight={targetHeight}");
                return;
            }
            
            float newWidth = originSize.x * targetHeight / originSize.y;
            if (newWidth > 10000 || targetHeight > 10000) // 防止尺寸过大
            {
                VFDebug.LogError($"#lll 计算尺寸过大: {newWidth}x{targetHeight}");
                return;
            }
            
            SetSize(newWidth, targetHeight);
        }

        public void SetAsWidth(float targetWidth)
        {
            var originSize = GetOriginalSize();
            // 添加尺寸验证
            if (originSize.x <= 0 || originSize.y <= 0 || targetWidth <= 0)
            {
                VFDebug.LogError($"#lll 尺寸参数异常: originSize={originSize}, targetWidth={targetWidth}");
                return;
            }
            
            float newHeight = originSize.y * targetWidth / originSize.x;
            if (targetWidth > 10000 || newHeight > 10000) // 防止尺寸过大
            {
                VFDebug.LogError($"#lll 计算尺寸过大: {targetWidth}x{newHeight}");
                return;
            }
            
            SetSize(targetWidth, newHeight);
        }
    }
}