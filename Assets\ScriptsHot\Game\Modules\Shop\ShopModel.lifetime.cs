using System.Collections;
using System.Collections.Generic;
using Msg.economic;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Shop
{
    public partial class ShopModel
    {
        private PB_LifetimeSubscriptionInfo lifetimeData;
        public bool IsLifeTime => 
            lifetimeData != null && lifetimeData.subscription_info != null;
        
        public void SetLifetimeData(PB_ShopInfoData data)
        {
            lifetimeData = data.lifetime_subscription_info;
            TmpLog(lifetimeData);

            // lifetimeData = new PB_LifetimeSubscriptionInfo();
            // lifetimeData.subscription_info = data.subscription_infos[0];
        }

        public PB_SubscriptionInfo GetLifetimeData()
        {
            if (lifetimeData == null)
            {
                return null;
            }
            return lifetimeData.subscription_info;
        }


        public void TmpLog(object s)
        {
            Debug.LogWarning($"!!!!!!!!!!!!!!!!!! --- {s}");
        }
    }
}
