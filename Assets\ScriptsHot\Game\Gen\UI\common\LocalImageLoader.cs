/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.common
{
    public partial class LocalImageLoader : ExtendedComponent
    {
        public static string pkgName => "common";
        public static string comName => "LocalImageLoader";
        public static string url => "ui://gu4fqlmfkcqiuvptfz";

        public GLoader ldrImage;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(LocalImageLoader));
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            ldrImage = GetChildAt(0) as GLoader;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            ldrImage = null;

            base.Dispose();
        }
    }
}