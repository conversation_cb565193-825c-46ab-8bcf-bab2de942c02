﻿

using Msg.explore;
using ScriptsHot.Game.Modules.Explore.State.Base;
using ScriptsHot.Game.Modules.Explore.State.ExploreChat;
using ScriptsHot.Game.Modules.Explore.State.ExploreMission;
using UIBind.Explore.Item;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Explore.ExploreType
{
    public class ExploreStoryEntity:ExploreNormalEntity
    {
        private ExploreItemUI _item => _ui as ExploreItemUI;
        public override void AddEvent()
        { 
            base.AddEvent();
    
            // Mission剧情对话下行 - 任务步骤进度变更
            MsgManager.instance.RegisterCallBack<SC_MissionStoryChatDownMsgForStepProgressChange>(this.OnStepProgressChange);
            // 对话结算
            MsgManager.instance.RegisterCallBack<SC_MissionStoryChatDownMsgForSettlement>(this.OnSettlement);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreStepStateChange,OnExploreStepStateChange);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreTaskChange,OnExploreTaskChange);
        }

        public override void UnEvent()
        {
            base.UnEvent();
            
            // Mission剧情对话下行 - 任务步骤进度变更
            MsgManager.instance.UnRegisterCallBack<SC_MissionStoryChatDownMsgForStepProgressChange>(this.OnStepProgressChange);
            // 对话结算
            // MsgManager.instance.UnRegisterCallBack<SC_MissionStoryChatDownMsgForSettlement>(this.OnSettlement);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreStepStateChange,OnExploreStepStateChange);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreTaskChange,OnExploreTaskChange);
        }
        
        protected override void InitStateMachine()
        {
            base.InitStateMachine();
            _stateMachine.AddState(new ExploreStateStart(this));
            _stateMachine.AddState(new ExploreMissionStart(this));
            _stateMachine.AddState(new ExploreStateExit(this));
        }

        /// <summary>
        /// 对话结算
        /// </summary>
        /// <param name="msg"></param>
        private void OnSettlement(SC_MissionStoryChatDownMsgForSettlement msg)
        {
            Debug.LogError("Mission剧情对话下行 - 结算");
            _controller.Model.SetSettlement(msg);
            
            _controller.Model.SetTaskChange(msg.nextEntityInfo.storyPreloadData);
            Debug.LogError($"avatar txt 对话结算 ::{msg.nextEntityInfo.storyPreloadData.firstRoundData.replyText} id:{ msg.nextEntityInfo.storyPreloadData.storyId}");
            Notifier.instance.SendNotification(NotifyConsts.ExploreEndDataRefresh);
         
        }
        
        /// <summary>
        /// task  变更
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnExploreTaskChange(string name, object body)
        {
            //数据 提前已经更新
            Notifier.instance.SendNotification(NotifyConsts.RefreshExploreDataAndEnter);
            //恢复麦克风状态
            _controller.OnCancelRecored();
        }


        /// <summary>
        /// Mission剧情对话下行 - 任务步骤进度变更
        /// </summary>
        /// <param name="msg"></param>
        private void OnStepProgressChange(SC_MissionStoryChatDownMsgForStepProgressChange msg)
        {
            Debug.LogError("Mission剧情对话下行 - 步骤进度变更");
            _controller.Model.SetProgressChange(msg);
        }

        protected void OnExploreStepStateChange(string name, object body)
        {
            Step = (ExploreStep) body;
            VFDebug.Log("Explore Step： " + Step);
            if (Step == ExploreStep.Begin)
            {
                _controller.CloseRecordUI();
            }
            else if (Step == ExploreStep.Player)
            {
                _controller.HideRecordUI();
            }
        }

    }
}

