/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreFriendContainer : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreFriendContainer";

        public GList listContainer;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            listContainer = (GList)com.GetChildAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            listContainer = null;
        }
    }
}