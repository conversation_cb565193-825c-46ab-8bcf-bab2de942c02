/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreSettlement
{
    public partial class CompExploreProgress : UIBindT
    {
        public override string pkgName => "ExploreSettlement";
        public override string comName => "CompExploreProgress";

        public Controller state;
        public GTextField tfProgress;
        public CompBar compBar;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);
            tfProgress = (GTextField)com.GetChildAt(3);
            compBar = new CompBar();
            compBar.Construct(com.GetChildAt(4).asCom);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
            tfProgress = null;
            compBar.Dispose();
            compBar = null;
        }
    }
}