﻿
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using ScriptsHot.Game.Modules.Explore.State.Base;
using ScriptsHot.Game.Modules.Procedure;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Explore.State.ExploreMission
{
    public class ExploreMissionStart:ExploreStateBase
    {
        private ExploreController _controller;
        
        private ExploreParam _param;

        private bool _isRuning = false;
        public ExploreMissionStart( ExploreEntityBase chat) : base(ExploreStateName.ExploreMissionStart, chat)
        {
        }
        
        public override void OnEnter(params object[] args)
        {
            base.OnEnter(args);
            _isRuning = false;
            // Debug.LogError("ExploreChatActiveStart");
            _param = (ExploreParam)args[0];
            _controller = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;
           
        }
        
        override public void Update(int interval)
        {
            if (_param != null && _param.Entity.UI.GetModelLoaded())
            {
                Run();
            }
        }

        private void Run()
        {
            if (_isRuning) return;
            _isRuning = true;
            
            VFDebug.Log($"ExploreChatActiveStart:: Run  模型加载完毕开始播放进入语音");
            ProcedureParams p = new ProcedureParams();
            p.param = _param;
            p.type = EProcedureType.ExploreEnterAudio;
            //清空队列
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            
            ProcedureParams p1 = new ProcedureParams();
            p1.param = _controller.IsSeniorPlayer;
            p1.type = EProcedureType.ExploreAvatarCallFirst;
            
            Notifier.instance.SendNotification(NotifyConsts.ExploreEnterAudioPlay);
            Notifier.instance.SendNotification(NotifyConsts.ExploreChatFristAvatarShow);
            Notifier.instance.SendNotification(NotifyConsts.ExploreStepStateChange,ExploreStep.Avatar);
        }

        public override void OnReEnter(params object[] args)
        {
            base.OnReEnter(args);
        }

        public override void OnExit()
        {
            base.OnExit();
            _isRuning = false;
        }
    }
}