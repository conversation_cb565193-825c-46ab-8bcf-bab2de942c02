/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreMemoryPanel : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreMemoryPanel";

        public Controller ctrl;
        public GGraph imgBG;
        public GTextField txtTitle;
        public GImage btnCloseimg;
        public GGraph btnClose;
        public ExploreMemoryItem item1;
        public ExploreMemoryItem item2;
        public ExploreMemoryItem item3;
        public GGroup container;
        public GGroup baseContainer;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrl = com.GetControllerAt(0);
            imgBG = (GGraph)com.GetChildAt(0);
            txtTitle = (GTextField)com.GetChildAt(3);
            btnCloseimg = (GImage)com.GetChildAt(4);
            btnClose = (GGraph)com.GetChildAt(5);
            item1 = new ExploreMemoryItem();
            item1.Construct(com.GetChildAt(6).asCom);
            item2 = new ExploreMemoryItem();
            item2.Construct(com.GetChildAt(7).asCom);
            item3 = new ExploreMemoryItem();
            item3.Construct(com.GetChildAt(8).asCom);
            container = (GGroup)com.GetChildAt(9);
            baseContainer = (GGroup)com.GetChildAt(10);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrl = null;
            imgBG = null;
            txtTitle = null;
            btnCloseimg = null;
            btnClose = null;
            item1.Dispose();
            item1 = null;
            item2.Dispose();
            item2 = null;
            item3.Dispose();
            item3 = null;
            container = null;
            baseContainer = null;
        }
    }
}