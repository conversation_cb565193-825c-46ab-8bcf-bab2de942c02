/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreStoryPro : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreStoryPro";

        public Controller ctrlBack;
        public Controller ctrlProColor;
        public Controller ctrlPerState;
        public GImage backPro;
        public GImage backProValue;
        public GTextField txtNum;
        public GTextField txtNumGray;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlBack = com.GetControllerAt(0);
            ctrlProColor = com.GetControllerAt(1);
            ctrlPerState = com.GetControllerAt(2);
            backPro = (GImage)com.GetChildAt(7);
            backProValue = (GImage)com.GetChildAt(8);
            txtNum = (GTextField)com.GetChildAt(9);
            txtNumGray = (GTextField)com.GetChildAt(10);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlBack = null;
            ctrlProColor = null;
            ctrlPerState = null;
            backPro = null;
            backProValue = null;
            txtNum = null;
            txtNumGray = null;
        }
    }
}