/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Shop
{
    public partial class SpeakPlanPromotionStep3Panel : UIBindT
    {
        public override string pkgName => "Shop";
        public override string comName => "SpeakPlanPromotionStep3Panel";

        public GImage imgBG;
        public GButton btnBack;
        public GRichTextField tfTitle;
        public GTextField tfNotify;
        public GButton btnBottom;
        public GTextField tfDesc;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GImage)com.GetChildAt(0);
            btnBack = (GButton)com.GetChildAt(1);
            tfTitle = (GRichTextField)com.GetChildAt(4);
            tfNotify = (GTextField)com.GetChildAt(7);
            btnBottom = (GButton)com.GetChildAt(8);
            tfDesc = (GTextField)com.GetChildAt(9);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            btnBack = null;
            tfTitle = null;
            tfNotify = null;
            btnBottom = null;
            tfDesc = null;
        }
    }
}