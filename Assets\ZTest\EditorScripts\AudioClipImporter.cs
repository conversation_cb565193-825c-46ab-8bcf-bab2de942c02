#if UNITY_EDITOR
using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using AnimationSystem;
using Newtonsoft.Json;
using System.Threading.Tasks;
using UnityEditor;

namespace ZTest
{
    /// <summary>
    /// AudioClip导入器
    /// 用于导入之前导出的音频文件和数据，并进行播放
    /// </summary>
    public class AudioClipImporter : MonoBehaviour
    {
        [Header("导入设置")]
        [SerializeField] private string importFileName = "exported_audio";
        
        [Header("状态显示")]
        [SerializeField] private bool isDataLoaded = false;
        [SerializeField] private bool isAudioLoaded = false;
        [SerializeField] private bool isCharacterReady = false;
        
        private DialogManager dialogManager;
        private TestScruot testScript;
        private AudioExportData loadedData;
        private AudioClip loadedAudioClip;
        private AudioSource audioSource;
        
        private void Awake()
        {
            // 获取DialogManager组件
            dialogManager = GetComponent<DialogManager>();
            if (dialogManager == null)
            {
                Debug.LogWarning($"AudioClipImporter: 在GameObject '{gameObject.name}' 上未找到DialogManager组件！");
            }
            
            // 全局搜索TestScruot脚本
            FindTestScript();
            
            // 获取或创建AudioSource组件
            InitializeAudioSource();
        }
        
        /// <summary>
        /// 全局搜索TestScruot脚本
        /// </summary>
        private void FindTestScript()
        {
            TestScruot[] scripts = FindObjectsOfType<TestScruot>();
            if (scripts.Length > 0)
            {
                testScript = scripts[0];
                Debug.Log($"找到TestScruot脚本：{testScript.gameObject.name}");
            }
            else
            {
                Debug.LogWarning("未找到TestScruot脚本！");
            }
        }
        
        /// <summary>
        /// 初始化音频输出相关脚本
        /// </summary>
        private void InitializeAudioSource()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                Debug.Log("自动添加AudioSource组件");
            }
            
            // 配置AudioSource
            audioSource.playOnAwake = false;
            audioSource.loop = false;
        }
        
        /// <summary>
        /// 加载音频数据和文件
        /// </summary>
        public async void LoadAudioData()
        {
            if (string.IsNullOrEmpty(importFileName))
            {
                Debug.LogError("导入文件名不能为空！");
                return;
            }
            
            try
            {
                // 获取脚本同级目录
                string scriptPath = Path.Combine(Application.dataPath, "ZTest", "Animation").Replace("\\", "/");
                
                // 构建文件路径
                string jsonPath = Path.Combine(scriptPath, importFileName + "-data.json").Replace("\\", "/");
                string audioPath = Path.Combine(scriptPath, importFileName + ".wav").Replace("\\", "/");
                
                // 加载JSON数据
                bool jsonSuccess = LoadJsonData(jsonPath);
                
                // 加载音频文件
                bool audioSuccess = LoadAudioFile(audioPath);
                
                if (jsonSuccess && audioSuccess)
                {
                    Debug.Log($"数据和音频加载成功！\n" +
                             $"角色ID: {loadedData.avatarId}\n" +
                             $"音频长度: {loadedAudioClip.length:F2}秒\n" +
                             $"对话数据: {loadedData.dialogData.Count}条");
                    
                    isDataLoaded = true;
                    isAudioLoaded = true;
                    
                    // 立即加载角色
                    await LoadRequiredCharacter();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载数据时发生异常：{ex.Message}");
                isDataLoaded = false;
                isAudioLoaded = false;
                isCharacterReady = false;
            }
        }
        
        /// <summary>
        /// 加载JSON数据文件
        /// </summary>
        private bool LoadJsonData(string jsonPath)
        {
            try
            {
                if (!File.Exists(jsonPath))
                {
                    Debug.LogError($"JSON文件不存在：{jsonPath}");
                    return false;
                }
                
                string jsonContent = File.ReadAllText(jsonPath);
                loadedData = JsonConvert.DeserializeObject<AudioExportData>(jsonContent);
                
                Debug.Log($"JSON数据加载成功：\n" +
                         $"Seed: {loadedData.seed}\n" +
                         $"AvatarId: {loadedData.avatarId}\n" +
                         $"AnimTypeId: {loadedData.animTypeId}\n" +
                         $"DialogData Count: {loadedData.dialogData.Count}");
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载JSON文件失败：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 加载音频文件
        /// </summary>
        private bool LoadAudioFile(string audioPath)
        {
            try
            {
                if (!File.Exists(audioPath))
                {
                    Debug.LogError($"音频文件不存在：{audioPath}");
                    return false;
                }
                
                // 在Editor环境下，通过AssetDatabase加载
                string relativePath = GetRelativeAssetPath(audioPath);
                loadedAudioClip = AssetDatabase.LoadAssetAtPath<AudioClip>(relativePath);
                
                if (loadedAudioClip != null)
                {
                    Debug.Log($"音频文件加载成功：{loadedAudioClip.name}");
                    return true;
                }
                else
                {
                    Debug.LogError($"无法加载音频文件：{relativePath}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载音频文件失败：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 播放加载的音频和对话数据
        /// </summary>
        public void PlayLoadedData()
        {
            if (!isDataLoaded || !isAudioLoaded)
            {
                Debug.LogError("请先加载数据和音频文件！");
                return;
            }
            
            if (!isCharacterReady)
            {
                Debug.LogError("角色未准备好，请重新读取数据！");
                return;
            }
            
            if (testScript == null)
            {
                Debug.LogError("TestScruot脚本引用为空！");
                return;
            }
            
            try
            {
                // 检查角色一致性
                bool characterMatches = CheckCharacterConsistency();
                
                if (!characterMatches)
                {
                    Debug.LogError($"当前角色不匹配！需要角色：{loadedData.avatarId}，当前角色：{testScript.GetCurrentCharacterId()}。请重新读取数据！");
                    return;
                }
                
                // 设置DialogManager的音频和对话数据
                SetupDialogManager();
                
                // 播放音频
                PlayAudio();
                
                Debug.Log("开始播放音频和对话数据！");
            }
            catch (Exception ex)
            {
                Debug.LogError($"播放时发生异常：{ex.Message}");
            }
        }
        
        /// <summary>
        /// 加载所需的角色
        /// </summary>
        private async Task LoadRequiredCharacter()
        {
            if (testScript == null)
            {
                Debug.LogError("TestScruot脚本引用为空，无法加载角色！");
                isCharacterReady = false;
                return;
            }
            
            if (loadedData == null)
            {
                Debug.LogError("数据未加载，无法确定要加载的角色！");
                isCharacterReady = false;
                return;
            }
            
            try
            {
                string targetCharacterId = loadedData.avatarId;
                string currentCharacterId = testScript.GetCurrentCharacterId();
                
                // 检查角色是否一致
                if (currentCharacterId != targetCharacterId)
                {
                    Debug.Log($"需要加载新角色：{targetCharacterId}（当前角色：{currentCharacterId}）");
                    
                    // 使用TestScruot的加载流程
                    bool loadSuccess = await LoadCharacterFromTestScript(targetCharacterId);
                    
                    if (loadSuccess)
                    {
                        isCharacterReady = true;
                        Debug.Log($"角色加载成功：{targetCharacterId}");
                    }
                    else
                    {
                        Debug.LogError($"角色加载失败：{targetCharacterId}");
                        isCharacterReady = false;
                    }
                }
                else
                {
                    Debug.Log($"角色已一致：{currentCharacterId}");
                    isCharacterReady = true;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"角色加载过程中发生异常：{ex.Message}");
                isCharacterReady = false;
            }
        }
        
        /// <summary>
        /// 检查角色一致性
        /// </summary>
        private bool CheckCharacterConsistency()
        {
            if (testScript == null || loadedData == null)
            {
                return false;
            }
            
            string targetCharacterId = loadedData.avatarId;
            string currentCharacterId = testScript.GetCurrentCharacterId();
            
            return currentCharacterId == targetCharacterId;
        }
        
        /// <summary>
        /// 使用TestScript的加载流程加载角色
        /// </summary>
        private async Task<bool> LoadCharacterFromTestScript(string characterId)
        {
            try
            {
                // 使用TestScruot的公共加载方法
                bool loadSuccess = await testScript.LoadCharacterAsync(characterId);
                
                if (loadSuccess)
                {
                    // 更新DialogManager引用
                    GameObject avatarRoot = GameObject.Find("RoleSlot");
                    if (avatarRoot != null)
                    {
                        dialogManager = avatarRoot.GetComponentInChildren<DialogManager>();
                        if (dialogManager == null)
                        {
                            Debug.LogWarning("加载的角色上未找到DialogManager，使用当前对象上的DialogManager");
                            dialogManager = GetComponent<DialogManager>();
                        }
                    }
                    
                    Debug.Log($"角色加载完成：{characterId}");
                    return true;
                }
                else
                {
                    Debug.LogError($"角色加载失败：{characterId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"角色加载异常：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 设置DialogManager的数据
        /// </summary>
        private void SetupDialogManager()
        {
            if (dialogManager == null)
            {
                GameObject avatarRoot = GameObject.Find("RoleSlot");
                if (avatarRoot != null)
                {
                    dialogManager = avatarRoot.GetComponentInChildren<DialogManager>();
                    if (dialogManager == null)
                    {
                        Debug.LogWarning("加载的角色上未找到DialogManager，使用当前对象上的DialogManager");
                        dialogManager = GetComponent<DialogManager>();
                    }
                }
            }
            
            if (dialogManager == null)
            {
                return;
            }
            
            // 设置当前音频
            dialogManager.currentClip = loadedAudioClip;
            
            // 设置对话数据
            if (loadedData.dialogData != null && loadedData.dialogData.Count > 0)
            {
                dialogManager.currSentences = new List<SingleSentenceInfo>(loadedData.dialogData);
                Debug.Log($"设置对话数据：{loadedData.dialogData.Count}条");
            }
            
            // 进入角色说话状态
            dialogManager.EnterCharacterSpeakingState();
            
            // 处理对话数据
            if (loadedData.dialogData != null && loadedData.dialogData.Count > 0)
            {
                dialogManager.ProcessDialogue(loadedData.dialogData);
                Debug.Log("开始处理对话数据");
            }
        }
        
        /// <summary>
        /// 播放音频
        /// </summary>
        private void PlayAudio()
        {
            if (audioSource == null)
            {
                Debug.LogError("AudioSource为空，无法播放音频！");
                return;
            }
            
            if (loadedAudioClip == null)
            {
                Debug.LogError("音频文件未加载，无法播放！");
                return;
            }
            
            audioSource.clip = loadedAudioClip;
            audioSource.Play();
            
            Debug.Log($"开始播放音频：{loadedAudioClip.name}，长度：{loadedAudioClip.length:F2}秒");
        }
        
        /// <summary>
        /// 停止播放
        /// </summary>
        public void StopPlayback()
        {
            if (audioSource != null && audioSource.isPlaying)
            {
                audioSource.Stop();
            }
            
            if (dialogManager != null)
            {
                dialogManager.EnterDialogueIdleState();
            }
            
            Debug.Log("停止播放");
        }
        
        /// <summary>
        /// 获取脚本所在目录
        /// </summary>
        private string GetScriptDirectory()
        {
            var script = MonoScript.FromMonoBehaviour(this);
            string scriptPath = AssetDatabase.GetAssetPath(script);
            return Path.GetDirectoryName(scriptPath);
        }
        
        /// <summary>
        /// 获取相对于Assets的路径
        /// </summary>
        private string GetRelativeAssetPath(string absolutePath)
        {
            string assetsPath = Application.dataPath;
            if (absolutePath.StartsWith(assetsPath))
            {
                return "Assets" + absolutePath.Substring(assetsPath.Length);
            }
            return absolutePath;
        }
        
        /// <summary>
        /// 获取当前状态信息
        /// </summary>
        public void LogStatusInfo()
        {
            string info = "AudioClipImporter状态信息：\n";
            info += $"数据已加载: {isDataLoaded}\n";
            info += $"音频已加载: {isAudioLoaded}\n";
            info += $"角色已准备: {isCharacterReady}\n";
            
            if (testScript != null)
            {
                info += $"TestScruot引用: {testScript.gameObject.name}\n";
                info += $"当前角色ID: {testScript.GetCurrentCharacterId()}\n";
            }
            else
            {
                info += "TestScruot引用: 未找到\n";
                info += "当前角色ID: 无法获取\n";
            }
            
            if (loadedData != null)
            {
                info += $"加载的数据: AvatarId={loadedData.avatarId}, Seed={loadedData.seed}, DialogData={loadedData.dialogData.Count}条\n";
            }
            
            if (loadedAudioClip != null)
            {
                info += $"加载的音频: {loadedAudioClip.name}, 长度={loadedAudioClip.length:F2}秒\n";
            }
            
            Debug.Log(info);
        }
    }
}

namespace ZTest.Editor
{
    /// <summary>
    /// AudioClipImporter的自定义Inspector
    /// </summary>
    [CustomEditor(typeof(AudioClipImporter))]
    public class AudioClipImporterEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("音频导入工具", EditorStyles.boldLabel);
            
            AudioClipImporter importer = (AudioClipImporter)target;
            
            // 状态显示
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("当前状态：", EditorStyles.boldLabel);
            
            var dataLoaded = serializedObject.FindProperty("isDataLoaded");
            var audioLoaded = serializedObject.FindProperty("isAudioLoaded");
            var characterReady = serializedObject.FindProperty("isCharacterReady");
            
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.Toggle("数据已加载", dataLoaded.boolValue);
            EditorGUILayout.Toggle("音频已加载", audioLoaded.boolValue);
            EditorGUILayout.Toggle("角色已准备", characterReady.boolValue);
            EditorGUI.EndDisabledGroup();
            
            // 显示角色一致性信息
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("角色信息：", EditorStyles.boldLabel);
            
            var testScript = importer.GetComponent<TestScruot>();
            if (testScript == null)
            {
                testScript = FindObjectOfType<TestScruot>();
            }
            
            if (testScript != null)
            {
                string currentCharacter = testScript.GetCurrentCharacterId();
                EditorGUILayout.LabelField($"当前场景角色：{(string.IsNullOrEmpty(currentCharacter) ? "无" : currentCharacter)}");
                
                // 获取需要的角色ID
                var loadedDataField = typeof(AudioClipImporter).GetField("loadedData", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (loadedDataField != null)
                {
                    var loadedData = loadedDataField.GetValue(importer) as AudioExportData;
                    if (loadedData != null)
                    {
                        bool isMatch = currentCharacter == loadedData.avatarId;
                        EditorGUILayout.LabelField($"需要角色：{loadedData.avatarId}");
                        
                        string statusText = isMatch ? "✓ 角色匹配" : "✗ 角色不匹配";
                        var statusStyle = isMatch ? EditorStyles.label : EditorStyles.helpBox;
                        EditorGUILayout.LabelField($"状态：{statusText}");
                        
                        if (!isMatch && dataLoaded.boolValue)
                        {
                            EditorGUILayout.HelpBox("角色不匹配！请重新加载数据。", MessageType.Warning);
                        }
                    }
                    else if (dataLoaded.boolValue)
                    {
                        EditorGUILayout.LabelField("需要角色：数据加载失败");
                    }
                }
            }
            else
            {
                EditorGUILayout.LabelField("TestScruot：未找到");
            }
            
            EditorGUILayout.Space();
            
            // 操作按钮
            EditorGUILayout.LabelField("操作：", EditorStyles.boldLabel);
            
            if (GUILayout.Button("加载音频数据和文件", GUILayout.Height(30)))
            {
                importer.LoadAudioData();
            }
            
            EditorGUILayout.Space();
            
            GUI.enabled = dataLoaded.boolValue && audioLoaded.boolValue && characterReady.boolValue;
            if (GUILayout.Button("播放加载的数据", GUILayout.Height(30)))
            {
                importer.PlayLoadedData();
            }
            GUI.enabled = true;
            
            if (GUILayout.Button("停止播放"))
            {
                importer.StopPlayback();
            }
            
            EditorGUILayout.Space();
            
            // 信息按钮
            if (GUILayout.Button("显示状态信息"))
            {
                importer.LogStatusInfo();
            }
            
            EditorGUILayout.Space();
            EditorGUILayout.HelpBox("使用步骤：\n1. 设置导入文件名（不含扩展名）\n2. 点击\"加载音频数据和文件\"（会自动加载角色）\n3. 等待角色加载完成\n4. 点击\"播放加载的数据\"（只有当前角色匹配才能播放）", MessageType.Info);
        }
    }
}
#endif 