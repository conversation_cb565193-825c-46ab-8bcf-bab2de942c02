﻿
using System.IO;
using UnityEngine;

public class AppConst
{
    //保存设备的原始分辨率
    public static int screenWidth = 0;
    public static int screenHeight = 0;
    public static float screenDpi = 0;

    //设计分辨率
    public const int designWidth = 750;
    public const int designHeight = 1624;

    //游戏帧率
    public static int FrameRate = 60;
    
    //grpc消息缓存功能开关
    public static bool CacheMsgEnable = true;
    //grpc消息最大缓存时间(毫秒)
    public static int MaxCacheDataTime = 3600; // 1小时

    public static string VersionHash = string.Empty;//运行时赋予的热更版本信息, 同一个clientversion下，ios和android的hash不同

    public static string PromoCodeEnableTag = "open";
    public static string ChatBgBackTag = "backupRoom";

    //极光推送国内版/海外版相关
    public static readonly string APPKEY_TALKIT_JPTEST = "a7af4090a104c394524175da";
    public static readonly string APPKEY_TALKIT_FR = "ce0e34ed9449b40ea3eebdba";
    
    //网络相关
    public static readonly int HttpPostTimeOutLv0 = 4;
    public static readonly int HttpPostTimeOutLv1 = 10;
    public static readonly int HttpPostTimeOutLv2 = 20;
#if UNITY_IOS
#if MACRO_JP
    public static string CrashsightAppID="7d223a964f";
    public static string AppsFlyerId = "6459055089";
#elif MACRO_FR_PROD
    public static string CrashsightAppID="25b99e517a";
    public static string AppsFlyerId = "6475726422";//此id为法国版id
#elif MACRO_FR_PRE
    public static string CrashsightAppID="25b99e517a";
    public static string AppsFlyerId = "6475726422";//此id为法国版id
#elif MACRO_QA
    public static string CrashsightAppID="1856f3748d";
    public static string AppsFlyerId = "6459055089";
#else
    public static string CrashsightAppID = "";
    public static string AppsFlyerId = "6459055089";
#endif
#elif UNITY_ANDROID
    public static string CrashsightAppID = "f4e1d0bdfc";
    public static string AppsFlyerId = "visionflow.ai";
#else
    public static string AppsFlyerId = "";
#endif
    
    #region appsflyer
    public static string DevKey = "BdMgLu8DzVcdkm9NF5iNw3";
    
    //后面可以放到下面宏里 用一个变量
    // public static string AppFlyerId_Fr = "6475726422";//此id为法国版id
    // public static string AppFlyerId_Jp = "6459055089";//此id为日本版id
    // public static string AppFlyerId_Fr_Android = "visionflow.ai";
    #endregion
    
    #region google sign
#if UNITY_IOS
    public static string WebClientId = "634074904393-vh8tg80nsj950nn3c4f07alsjgefgh9p.apps.googleusercontent.com";
#elif UNITY_ANDROID
    public static string WebClientId = "634074904393-slht71r4kte07ghcq6our5ir2pi8m1uk.apps.googleusercontent.com";
#else
    public static string WebClientId = "";
#endif
    #endregion
    
    public static bool IsNewHomepageLogic = true;

#if MACRO_JP
    public static string HotUpdate_version_file_name = "version_jp.v";
    public static bool IsDebug = true;
    public static bool IsAppsFlyerDebug = true;
    public static bool IsLogMsg = true;
    public static bool IsDebugDevice = true;
    public static bool IsPublicDebugSrv = true;
    public static string AppChannel = "apple";
    public static string LoginSrv = "https://login-uat.talkit.ai"; 
    public static string WebViewURL = "https://jp-uat-pg.talkit.ai";
    public static string ShareURL = "http://talkit-jp-uat.talkit.ai";
    public static string DebugPromoCodeInfo ="open";
    public static bool IsProd = false;
#elif MACRO_FR_PROD
    public static string HotUpdate_version_file_name = "version_fr_prod.v";
    public static bool IsDebug = true;
    public static bool IsAppsFlyerDebug = true;
    public static bool IsLogMsg = true;
    public static bool IsDebugDevice = true;
    public static bool IsPublicDebugSrv = true;
    public static string AppChannel = "apple";
    public static string LoginSrv = "https://login-prod.talkit.ai";
    public static string WebViewURL = "https://fr-prod-pg.talkit.ai";
    public static string ShareURL = "http://talkit-fr-prod-k8s.talkit.ai";
    public static string DebugPromoCodeInfo = string.Empty;//是否开放promo状态的开关，prod版本默认为空
    public static bool IsProd = true;
#elif MACRO_FR_PRE
    public static string HotUpdate_version_file_name = "version_fr_pre.v";
    public static bool IsDebug = true;
    public static bool IsAppsFlyerDebug = false;
    public static bool IsLogMsg = true;
    public static bool IsDebugDevice = false;
    public static bool IsPublicDebugSrv = false;
    public static string AppChannel = "apple";
    public static string LoginSrv = "https://login-pre.talkit.ai";
    public static string WebViewURL = "https://fr-pre-pg.talkit.ai";
    public static string ShareURL = "http://talkit-fr-pre.talkit.ai";
    public static string DebugPromoCodeInfo = string.Empty;//是否开放promo状态的开关，prod版本默认为空
    public static bool IsProd = false;
#elif MACRO_QA
    public static string HotUpdate_version_file_name = "version_qa.v";
    public static bool IsDebug = true;
    public static bool IsAppsFlyerDebug = true;
    public static bool IsLogMsg = true;
    public static bool IsDebugDevice = true;
    public static bool IsPublicDebugSrv = true;
    public static string AppChannel = "debug";//qa 登录时也启用白名单数字id的登录模式
    public static string LoginSrv = "https://login-qa.talkit.ai";
    public static string WebViewURL = "https://jp-qa-pg.talkit.ai";
    public static string ShareURL = "http://talkit-jp-qa.talkit.ai";
    public static string DebugPromoCodeInfo ="open";
    public static bool IsProd = false;
#else
    public static string HotUpdate_version_file_name = "version_qa.v";
    public static bool IsDebug = true;
    public static bool IsAppsFlyerDebug = true;
    public static bool IsLogMsg = true;
    public static bool IsDebugDevice = true;
    public static bool IsPublicDebugSrv = true; //是否用 gm面板的debugSrvHost，以及是否允许用摇一摇
    public static string AppChannel = "debug";
    public static bool IsProd = false; //是否是内部环境,初始化FireBase用

    // public static string LoginSrv = "http://*********:8888";  //测试环境 jp-qa
    public static string LoginSrv = "https://login-qa.talkit.ai";  //测试环境 jp-qa
    public static string DebugPromoCodeInfo = "open";//
    public static string WebViewURL = "https://jp-qa-pg.talkit.ai";
    public static string ShareURL = "http://talkit-jp-qa.talkit.ai";
#endif

    public static string HotfixUrlV2 = $"{LoginSrv}/account/gethotfixV2";
    public static string HotUpdate_version_url = $"https://jp-visionflow-resource-cnd.talkit.ai/version_folder/{HotUpdate_version_file_name}";
#if UNITY_EDITOR
    public static bool IsHotUpdateMode = false;
#else
#if MACRO_ENABLE_HOTUPDATE
        public static bool IsHotUpdateMode = true;
#else
    public static bool IsHotUpdateMode = false;
#endif
#endif

#if PHONE4CN
    public static bool IsCN = true;
#else
    public static bool IsCN = false;
#endif
    
#if MACRO_FR_PROD || MACRO_FR_PRE
    public static bool AllowShowChattingTab = false;
#else
    public static bool AllowShowChattingTab = true;
#endif
    
#if MACRO_GPAD
    public static bool USE_AAB = true;
#else
    public static bool USE_AAB = false;
#endif
    
    public static void EnableDebug() {
        IsDebug = true;
        if (!SRDebug.IsInitialized)
        {
            SRDebug.Init();
        }
    }
}
