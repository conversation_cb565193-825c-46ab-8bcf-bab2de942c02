#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
public class COLOR : MonoBehaviour
{
    public Color color1;
    public Color color2;
    public Color color3;
    public Color color4;
    public Color color5;
}


[CustomEditor(typeof(COLOR))]
public class COLOREditor : Editor
{
    private COLOR colorComponent;
    private Vector4 sRGBValues = Vector4.one;
    private Vector4 linearValues = Vector4.one;
    private bool useSRGB = true;

    private void OnEnable()
    {
        colorComponent = (COLOR)target;
    }

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("颜色转换工具", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // 选择转换模式
        useSRGB = EditorGUILayout.Toggle("使用 sRGB 输入", useSRGB);
        EditorGUILayout.Space();

        if (useSRGB)
        {
            // sRGB 输入模式
            EditorGUILayout.LabelField("sRGB 值 (0-1):", EditorStyles.boldLabel);
            sRGBValues = EditorGUILayout.Vector4Field("sRGB", sRGBValues);
            
            // 实时转换为 Linear
            linearValues = SRGBToLinear(sRGBValues);
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Linear 值:", EditorStyles.boldLabel);
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.Vector4Field("Linear", linearValues);
            EditorGUI.EndDisabledGroup();
            
            // 显示颜色预览
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("颜色预览:", EditorStyles.boldLabel);
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.ColorField("sRGB 颜色", new Color(sRGBValues.x, sRGBValues.y, sRGBValues.z, sRGBValues.w));
            EditorGUILayout.ColorField("Linear 颜色", new Color(linearValues.x, linearValues.y, linearValues.z, linearValues.w));
            EditorGUI.EndDisabledGroup();
        }
        else
        {
            // Linear 输入模式
            EditorGUILayout.LabelField("Linear 值 (0-1):", EditorStyles.boldLabel);
            linearValues = EditorGUILayout.Vector4Field("Linear", linearValues);
            
            // 实时转换为 sRGB
            sRGBValues = LinearToSRGB(linearValues);
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("sRGB 值:", EditorStyles.boldLabel);
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.Vector4Field("sRGB", sRGBValues);
            EditorGUI.EndDisabledGroup();
            
            // 显示颜色预览
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("颜色预览:", EditorStyles.boldLabel);
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.ColorField("Linear 颜色", new Color(linearValues.x, linearValues.y, linearValues.z, linearValues.w));
            EditorGUILayout.ColorField("sRGB 颜色", new Color(sRGBValues.x, sRGBValues.y, sRGBValues.z, sRGBValues.w));
            EditorGUI.EndDisabledGroup();
        }

        EditorGUILayout.Space();
        
        // 应用按钮
        if (GUILayout.Button("应用到 color1"))
        {
            colorComponent.color1 = useSRGB ? new Color(sRGBValues.x, sRGBValues.y, sRGBValues.z, sRGBValues.w) 
                                           : new Color(linearValues.x, linearValues.y, linearValues.z, linearValues.w);
            EditorUtility.SetDirty(colorComponent);
        }
        
        if (GUILayout.Button("应用到 color2"))
        {
            colorComponent.color2 = useSRGB ? new Color(sRGBValues.x, sRGBValues.y, sRGBValues.z, sRGBValues.w) 
                                           : new Color(linearValues.x, linearValues.y, linearValues.z, linearValues.w);
            EditorUtility.SetDirty(colorComponent);
        }
        
        if (GUILayout.Button("应用到 color3"))
        {
            colorComponent.color3 = useSRGB ? new Color(sRGBValues.x, sRGBValues.y, sRGBValues.z, sRGBValues.w) 
                                           : new Color(linearValues.x, linearValues.y, linearValues.z, linearValues.w);
            EditorUtility.SetDirty(colorComponent);
        }
        
        if (GUILayout.Button("应用到 color4"))
        {
            colorComponent.color4 = useSRGB ? new Color(sRGBValues.x, sRGBValues.y, sRGBValues.z, sRGBValues.w) 
                                           : new Color(linearValues.x, linearValues.y, linearValues.z, linearValues.w);
            EditorUtility.SetDirty(colorComponent);
        }
        
        if (GUILayout.Button("应用到 color5"))
        {
            colorComponent.color5 = useSRGB ? new Color(sRGBValues.x, sRGBValues.y, sRGBValues.z, sRGBValues.w) 
                                           : new Color(linearValues.x, linearValues.y, linearValues.z, linearValues.w);
            EditorUtility.SetDirty(colorComponent);
        }
    }

    // sRGB 转 Linear 转换函数
    private Vector4 SRGBToLinear(Vector4 sRGB)
    {
        Vector4 linear = Vector4.zero;
        
        for (int i = 0; i < 4; i++)
        {
            float value = sRGB[i];
            if (value <= 0.04045f)
            {
                linear[i] = value / 12.92f;
            }
            else
            {
                linear[i] = Mathf.Pow((value + 0.055f) / 1.055f, 2.4f);
            }
        }
        
        return linear;
    }

    // Linear 转 sRGB 转换函数
    private Vector4 LinearToSRGB(Vector4 linear)
    {
        Vector4 sRGB = Vector4.zero;
        
        for (int i = 0; i < 4; i++)
        {
            float value = linear[i];
            if (value <= 0.0031308f)
            {
                sRGB[i] = value * 12.92f;
            }
            else
            {
                sRGB[i] = 1.055f * Mathf.Pow(value, 1f / 2.4f) - 0.055f;
            }
        }
        
        return sRGB;
    }
}
#endif
