#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using AnimationSystem;

public class DialogJsonConverter : EditorWindow
{
    private string inputJson = "";
    private string outputJson = "";
    private Vector2 inputScrollPos;
    private Vector2 outputScrollPos;
    private int inputModeIndex = 0;
    private string[] inputModes = { "AI Text Format", "GPT JSON Format" };

    [MenuItem("Tools/MeEditor/Dialog JSON Converter")]
    public static void ShowWindow()
    {
        GetWindow<DialogJsonConverter>("Dialog JSON Converter");
    }

    void OnGUI()
    {
        GUILayout.Label("Dialog JSON Converter", EditorStyles.boldLabel);
        
        EditorGUILayout.Space();
        
        // 输入模式选择
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("Input Mode:", GUILayout.Width(80));
        inputModeIndex = EditorGUILayout.Popup(inputModeIndex, inputModes);
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        
        // 输入框区域
        string inputLabel = inputModeIndex == 0 ? "Input (AI Text Format):" : "Input (GPT JSON Format):";
        GUILayout.Label(inputLabel, EditorStyles.label);
        inputScrollPos = EditorGUILayout.BeginScrollView(inputScrollPos, GUILayout.Height(200));
        inputJson = EditorGUILayout.TextArea(inputJson, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
        
        EditorGUILayout.Space();
        
        // 转换按钮
        if (GUILayout.Button("Convert to New Format", GUILayout.Height(30)))
        {
            ConvertJson();
        }
        
        EditorGUILayout.Space();
        
        // 输出框区域
        GUILayout.Label("Output (New Format JSON):", EditorStyles.label);
        outputScrollPos = EditorGUILayout.BeginScrollView(outputScrollPos, GUILayout.Height(200));
        EditorGUILayout.SelectableLabel(outputJson, EditorStyles.textArea, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
        
        EditorGUILayout.Space();
        
        // 复制按钮
        if (GUILayout.Button("Copy Output to Clipboard") && !string.IsNullOrEmpty(outputJson))
        {
            EditorGUIUtility.systemCopyBuffer = outputJson;
            Debug.Log("输出JSON已复制到剪贴板");
        }
        
        // 示例按钮
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Load AI Text Example"))
        {
            LoadExampleAiText();
        }
        if (GUILayout.Button("Load JSON Example"))
        {
            LoadExampleJson();
        }
        EditorGUILayout.EndHorizontal();
    }

    private void ConvertJson()
    {
        if (string.IsNullOrEmpty(inputJson))
        {
            Debug.LogWarning("请输入数据");
            return;
        }

        if (inputModeIndex == 0) // AI Text Format
        {
            // 使用新的简化转换：直接从AI文本到NewSingleSentenceInfo JSON
            outputJson = Util.ConvertAiTextToNewSingleSentenceInfo(inputJson);
        }
        else // GPT JSON Format
        {
            // 保留原有逻辑：GPT JSON → NewSingleSentenceInfo JSON
            outputJson = Util.ConvertGptJsonToNewSingleSentenceInfo(inputJson);
        }
        
        if (string.IsNullOrEmpty(outputJson))
        {
            Debug.LogError("JSON转换失败，请检查输入格式");
        }
        else
        {
            Debug.Log("JSON转换成功");
        }
    }

    private void LoadExampleAiText()
    {
        inputJson = @"Sentence:Hey there, I'm Kenji.
  - Segment: there, Group: state/default, Trigger: Hey
  - Segment: Kenji, Group: PointSelf, Trigger: I'm

Sentence:Did you catch any World Cup matches?
  - Segment: matches, Group: PointPlayer, Trigger: Did";
        inputModeIndex = 0; // 切换到AI Text模式
        Debug.Log("已加载AI文本示例");
    }

    private void LoadExampleJson()
    {
        inputJson = @"{
  ""sentences"": [
    {
      ""text"": ""Hey there, I'm Kenji."",
      ""segments"": [
        {
          ""text"": ""there"",
          ""gesture_group"": ""state/default"",
          ""trigger_term"": ""Hey""
        },
        {
          ""text"": ""Kenji"",
          ""gesture_group"": ""PointSelf"",
          ""trigger_term"": ""I'm""
        }
      ]
    },
    {
      ""text"": ""Did you catch any World Cup matches?"",
      ""segments"": [
        {
          ""text"": ""matches"",
          ""gesture_group"": ""PointPlayer"",
          ""trigger_term"": ""Did""
        }
      ]
    }
  ]
}";
        inputModeIndex = 1; // 切换到GPT JSON模式
        Debug.Log("已加载示例JSON");
    }
}
#endif