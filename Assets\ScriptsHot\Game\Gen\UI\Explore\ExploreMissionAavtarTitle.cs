/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreMissionAavtarTitle : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreMissionAavtarTitle";

        public Controller ctrlFriendLevel;
        public GImage back;
        public GTextField txtAvatarName;
        public GTextField txtLevel;
        public GGroup txtGroup;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlFriendLevel = com.GetControllerAt(0);
            back = (GImage)com.GetChildAt(0);
            txtAvatarName = (GTextField)com.GetChildAt(1);
            txtLevel = (GTextField)com.GetChildAt(2);
            txtGroup = (GGroup)com.GetChildAt(5);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlFriendLevel = null;
            back = null;
            txtAvatarName = null;
            txtLevel = null;
            txtGroup = null;
        }
    }
}