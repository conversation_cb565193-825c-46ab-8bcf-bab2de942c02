﻿namespace ScriptsHot.Game.Modules.Procedure.Drama
{
    public class DramaExploreChatEnd : BaseDrama
    {
        private ulong _audioId;
        public override void OnEvent()
        {
            base.OnEvent();
        }

        public override void Do(bool canFinish = true)
        {
            base.Do();
            Notifier.instance.SendNotification(NotifyConsts.OpenUI, UIConsts.ExploreSettlementUI);
        }

        public override void OnFinish(bool isBreak)
        {
            base.OnFinish(isBreak);
        }
        
        public override void Dispose()
        {
            base.Dispose();
        }
    }
}

