#if UNITY_EDITOR
using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using AnimationSystem;
using Newtonsoft.Json;
using System.Threading.Tasks;
using UnityEditor;

namespace ZTest
{
    /// <summary>
    /// 带自定义JSON的AudioClip导入器
    /// 用于导入音频文件，但使用自定义JSON而不是data文件中的dialogData
    /// </summary>
    public class AudioClipImporterWithCustomJson : MonoBehaviour
    {
        [Header("导入设置")]
        [SerializeField] private string importFileName = "exported_audio";
        
        [Header("自定义JSON数据")]
        [TextArea(10, 20)]
        [SerializeField] private string customJsonData = "";
        
        [Header("状态显示")]
        [SerializeField] private bool isDataLoaded = false;
        [SerializeField] private bool isAudioLoaded = false;
        [SerializeField] private bool isCharacterReady = false;
        [SerializeField] private bool isJsonParsed = false;
        
        private DialogManager dialogManager;
        private TestScruot testScript;
        private AudioExportData loadedData;
        private AudioClip loadedAudioClip;
        private AudioSource audioSource;
        private List<NewSingleSentenceInfo> customDialogData;
        
        private void Awake()
        {
            // 获取DialogManager组件
            dialogManager = GetComponent<DialogManager>();
            if (dialogManager == null)
            {
                Debug.LogWarning($"AudioClipImporterWithCustomJson: 在GameObject '{gameObject.name}' 上未找到DialogManager组件！");
            }
            
            // 全局搜索TestScruot脚本
            FindTestScript();
            
            // 获取或创建AudioSource组件
            InitializeAudioSource();
        }
        
        /// <summary>
        /// 全局搜索TestScruot脚本
        /// </summary>
        private void FindTestScript()
        {
            TestScruot[] scripts = FindObjectsOfType<TestScruot>();
            if (scripts.Length > 0)
            {
                testScript = scripts[0];
                Debug.Log($"找到TestScruot脚本：{testScript.gameObject.name}");
            }
            else
            {
                Debug.LogWarning("未找到TestScruot脚本！");
            }
        }
        
        /// <summary>
        /// 初始化音频输出相关脚本
        /// </summary>
        private void InitializeAudioSource()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                Debug.Log("自动添加AudioSource组件");
            }
            
            // 配置AudioSource
            audioSource.playOnAwake = false;
            audioSource.loop = false;
        }
        
        /// <summary>
        /// 解析自定义JSON数据
        /// </summary>
        public bool ParseCustomJson()
        {
            if (string.IsNullOrEmpty(customJsonData))
            {
                Debug.LogError("自定义JSON数据不能为空！");
                isJsonParsed = false;
                return false;
            }
            
            try
            {
                // 解析JSON，提取sentences部分
                var jsonObject = Newtonsoft.Json.Linq.JObject.Parse(customJsonData);
                var sentencesToken = jsonObject["sentences"];
                
                if (sentencesToken == null)
                {
                    Debug.LogError("JSON中未找到 'sentences' 字段！");
                    isJsonParsed = false;
                    return false;
                }
                
                // 将sentences部分反序列化为NewSingleSentenceInfo列表
                customDialogData = sentencesToken.ToObject<List<NewSingleSentenceInfo>>();
                
                if (customDialogData != null && customDialogData.Count > 0)
                {
                    Debug.Log($"自定义JSON解析成功：{customDialogData.Count}条对话数据");
                    isJsonParsed = true;
                    return true;
                }
                else
                {
                    Debug.LogError("解析后的对话数据为空！");
                    isJsonParsed = false;
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"解析自定义JSON失败：{ex.Message}");
                isJsonParsed = false;
                return false;
            }
        }
        
        /// <summary>
        /// 加载音频数据和文件
        /// </summary>
        public async void LoadAudioData()
        {
            if (string.IsNullOrEmpty(importFileName))
            {
                Debug.LogError("导入文件名不能为空！");
                return;
            }
            
            // 先解析自定义JSON
            if (!ParseCustomJson())
            {
                Debug.LogError("自定义JSON解析失败，无法继续！");
                return;
            }
            
            try
            {
                // 获取脚本同级目录
                string scriptPath = "Assets\\ZTest\\Animation";
                
                // 构建文件路径
                string jsonPath = Path.Combine(scriptPath, importFileName + "-data.json");
                string audioPath = Path.Combine(scriptPath, importFileName + ".wav");
                
                // 加载JSON数据（只用于获取avatarId等元数据）
                bool jsonSuccess = LoadJsonData(jsonPath);
                
                // 加载音频文件
                bool audioSuccess = LoadAudioFile(audioPath);
                
                if (jsonSuccess && audioSuccess && isJsonParsed)
                {
                    Debug.Log($"数据和音频加载成功！\n" +
                             $"角色ID: {loadedData.avatarId}\n" +
                             $"音频长度: {loadedAudioClip.length:F2}秒\n" +
                             $"自定义对话数据: {customDialogData.Count}条");
                    
                    isDataLoaded = true;
                    isAudioLoaded = true;
                    
                    // 立即加载角色
                    await LoadRequiredCharacter();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载数据时发生异常：{ex.Message}");
                isDataLoaded = false;
                isAudioLoaded = false;
                isCharacterReady = false;
                isJsonParsed = false;
            }
        }
        
        /// <summary>
        /// 加载JSON数据文件（仅用于获取元数据）
        /// </summary>
        private bool LoadJsonData(string jsonPath)
        {
            try
            {
                if (!File.Exists(jsonPath))
                {
                    Debug.LogError($"JSON文件不存在：{jsonPath}");
                    return false;
                }
                
                string jsonContent = File.ReadAllText(jsonPath);
                loadedData = JsonConvert.DeserializeObject<AudioExportData>(jsonContent);
                
                Debug.Log($"JSON元数据加载成功：\n" +
                         $"Seed: {loadedData.seed}\n" +
                         $"AvatarId: {loadedData.avatarId}\n" +
                         $"AnimTypeId: {loadedData.animTypeId}\n" +
                         $"原始DialogData Count: {loadedData.dialogData.Count}（将被自定义JSON覆盖）");
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载JSON文件失败：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 加载音频文件
        /// </summary>
        private bool LoadAudioFile(string audioPath)
        {
            try
            {
                if (!File.Exists(audioPath))
                {
                    Debug.LogError($"音频文件不存在：{audioPath}");
                    return false;
                }
                
                // 在Editor环境下，通过AssetDatabase加载
                string relativePath = GetRelativeAssetPath(audioPath);
                loadedAudioClip = AssetDatabase.LoadAssetAtPath<AudioClip>(relativePath);
                
                if (loadedAudioClip != null)
                {
                    Debug.Log($"音频文件加载成功：{loadedAudioClip.name}");
                    return true;
                }
                else
                {
                    Debug.LogError($"无法加载音频文件：{relativePath}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载音频文件失败：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 播放加载的音频和自定义对话数据
        /// </summary>
        public void PlayLoadedData()
        {
            if (!isDataLoaded || !isAudioLoaded || !isJsonParsed)
            {
                Debug.LogError("请先加载数据、音频文件并解析自定义JSON！");
                return;
            }
            
            if (!isCharacterReady)
            {
                Debug.LogError("角色未准备好，请重新读取数据！");
                return;
            }
            
            if (testScript == null)
            {
                Debug.LogError("TestScruot脚本引用为空！");
                return;
            }
            
            try
            {
                // 检查角色一致性
                bool characterMatches = CheckCharacterConsistency();
                
                if (!characterMatches)
                {
                    Debug.LogError($"当前角色不匹配！需要角色：{loadedData.avatarId}，当前角色：{testScript.GetCurrentCharacterId()}。请重新读取数据！");
                    return;
                }
                
                // 设置DialogManager的音频和自定义对话数据
                SetupDialogManager();
                
                // 播放音频
                PlayAudio();
                
                Debug.Log("开始播放音频和自定义对话数据！");
            }
            catch (Exception ex)
            {
                Debug.LogError($"播放时发生异常：{ex.Message}");
            }
        }
        
        /// <summary>
        /// 加载所需的角色
        /// </summary>
        private async Task LoadRequiredCharacter()
        {
            if (testScript == null)
            {
                Debug.LogError("TestScruot脚本引用为空，无法加载角色！");
                isCharacterReady = false;
                return;
            }
            
            if (loadedData == null)
            {
                Debug.LogError("数据未加载，无法确定要加载的角色！");
                isCharacterReady = false;
                return;
            }
            
            try
            {
                string targetCharacterId = loadedData.avatarId;
                string currentCharacterId = testScript.GetCurrentCharacterId();
                
                // 检查角色是否一致
                if (currentCharacterId != targetCharacterId)
                {
                    Debug.Log($"需要加载新角色：{targetCharacterId}（当前角色：{currentCharacterId}）");
                    
                    // 使用TestScruot的加载流程
                    bool loadSuccess = await LoadCharacterFromTestScript(targetCharacterId);
                    
                    if (loadSuccess)
                    {
                        isCharacterReady = true;
                        Debug.Log($"角色加载成功：{targetCharacterId}");
                    }
                    else
                    {
                        Debug.LogError($"角色加载失败：{targetCharacterId}");
                        isCharacterReady = false;
                    }
                }
                else
                {
                    Debug.Log($"角色已一致：{currentCharacterId}");
                    isCharacterReady = true;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"角色加载过程中发生异常：{ex.Message}");
                isCharacterReady = false;
            }
        }
        
        /// <summary>
        /// 检查角色一致性
        /// </summary>
        private bool CheckCharacterConsistency()
        {
            if (testScript == null || loadedData == null)
            {
                return false;
            }
            
            string targetCharacterId = loadedData.avatarId;
            string currentCharacterId = testScript.GetCurrentCharacterId();
            
            return currentCharacterId == targetCharacterId;
        }
        
        /// <summary>
        /// 使用TestScript的加载流程加载角色
        /// </summary>
        private async Task<bool> LoadCharacterFromTestScript(string characterId)
        {
            try
            {
                // 使用TestScruot的公共加载方法
                bool loadSuccess = await testScript.LoadCharacterAsync(characterId);
                
                if (loadSuccess)
                {
                    // 更新DialogManager引用
                    GameObject avatarRoot = GameObject.Find("RoleSlot");
                    if (avatarRoot != null)
                    {
                        dialogManager = avatarRoot.GetComponentInChildren<DialogManager>();
                        if (dialogManager == null)
                        {
                            Debug.LogWarning("加载的角色上未找到DialogManager，使用当前对象上的DialogManager");
                            dialogManager = GetComponent<DialogManager>();
                        }
                    }
                    
                    Debug.Log($"角色加载完成：{characterId}");
                    return true;
                }
                else
                {
                    Debug.LogError($"角色加载失败：{characterId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"角色加载异常：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 设置DialogManager的数据（使用自定义JSON数据）
        /// </summary>
        private void SetupDialogManager()
        {
            if (dialogManager == null)
            {
                GameObject avatarRoot = GameObject.Find("RoleSlot");
                if (avatarRoot != null)
                {
                    dialogManager = avatarRoot.GetComponentInChildren<DialogManager>();
                    if (dialogManager == null)
                    {
                        Debug.LogWarning("加载的角色上未找到DialogManager，使用当前对象上的DialogManager");
                        dialogManager = GetComponent<DialogManager>();
                    }
                }
            }
            
            if (dialogManager == null)
            {
                return;
            }
            
            // 设置当前音频
            dialogManager.currentClip = loadedAudioClip;
            
            // 进入角色说话状态
            dialogManager.EnterCharacterSpeakingState();
            
            // 使用自定义对话数据调用重写的ProcessDialogue方法
            if (customDialogData != null && customDialogData.Count > 0)
            {
                dialogManager.ProcessDialogue(customDialogData);
                Debug.Log($"开始处理自定义对话数据：{customDialogData.Count}条");
            }
            else
            {
                Debug.LogError("自定义对话数据为空！");
            }
        }
        
        /// <summary>
        /// 播放音频
        /// </summary>
        private void PlayAudio()
        {
            if (audioSource == null)
            {
                Debug.LogError("AudioSource为空，无法播放音频！");
                return;
            }
            
            if (loadedAudioClip == null)
            {
                Debug.LogError("音频文件未加载，无法播放！");
                return;
            }
            
            audioSource.clip = loadedAudioClip;
            audioSource.Play();
            
            Debug.Log($"开始播放音频：{loadedAudioClip.name}，长度：{loadedAudioClip.length:F2}秒");
        }
        
        /// <summary>
        /// 停止播放
        /// </summary>
        public void StopPlayback()
        {
            if (audioSource != null && audioSource.isPlaying)
            {
                audioSource.Stop();
            }
            
            if (dialogManager != null)
            {
                dialogManager.EnterDialogueIdleState();
            }
            
            Debug.Log("停止播放");
        }
        
        /// <summary>
        /// 获取脚本所在目录
        /// </summary>
        private string GetScriptDirectory()
        {
            var script = MonoScript.FromMonoBehaviour(this);
            string scriptPath = AssetDatabase.GetAssetPath(script);
            return Path.GetDirectoryName(scriptPath);
        }
        
        /// <summary>
        /// 获取相对于Assets的路径
        /// </summary>
        private string GetRelativeAssetPath(string absolutePath)
        {
            string assetsPath = Application.dataPath;
            if (absolutePath.StartsWith(assetsPath))
            {
                return "Assets" + absolutePath.Substring(assetsPath.Length);
            }
            return absolutePath;
        }
        
        /// <summary>
        /// 获取当前状态信息
        /// </summary>
        public void LogStatusInfo()
        {
            string info = "AudioClipImporterWithCustomJson状态信息：\n";
            info += $"数据已加载: {isDataLoaded}\n";
            info += $"音频已加载: {isAudioLoaded}\n";
            info += $"角色已准备: {isCharacterReady}\n";
            info += $"JSON已解析: {isJsonParsed}\n";
            
            if (testScript != null)
            {
                info += $"TestScruot引用: {testScript.gameObject.name}\n";
                info += $"当前角色ID: {testScript.GetCurrentCharacterId()}\n";
            }
            else
            {
                info += "TestScruot引用: 未找到\n";
                info += "当前角色ID: 无法获取\n";
            }
            
            if (loadedData != null)
            {
                info += $"加载的元数据: AvatarId={loadedData.avatarId}, Seed={loadedData.seed}\n";
            }
            
            if (loadedAudioClip != null)
            {
                info += $"加载的音频: {loadedAudioClip.name}, 长度={loadedAudioClip.length:F2}秒\n";
            }
            
            if (customDialogData != null)
            {
                info += $"自定义对话数据: {customDialogData.Count}条\n";
            }
            
            Debug.Log(info);
        }
    }
}

namespace ZTest.Editor
{
    /// <summary>
    /// AudioClipImporterWithCustomJson的自定义Inspector
    /// </summary>
    [CustomEditor(typeof(AudioClipImporterWithCustomJson))]
    public class AudioClipImporterWithCustomJsonEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("自定义JSON音频导入工具", EditorStyles.boldLabel);
            
            AudioClipImporterWithCustomJson importer = (AudioClipImporterWithCustomJson)target;
            
            // 状态显示
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("当前状态：", EditorStyles.boldLabel);
            
            var dataLoaded = serializedObject.FindProperty("isDataLoaded");
            var audioLoaded = serializedObject.FindProperty("isAudioLoaded");
            var characterReady = serializedObject.FindProperty("isCharacterReady");
            var jsonParsed = serializedObject.FindProperty("isJsonParsed");
            
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.Toggle("数据已加载", dataLoaded.boolValue);
            EditorGUILayout.Toggle("音频已加载", audioLoaded.boolValue);
            EditorGUILayout.Toggle("角色已准备", characterReady.boolValue);
            EditorGUILayout.Toggle("JSON已解析", jsonParsed.boolValue);
            EditorGUI.EndDisabledGroup();
            
            // 显示角色一致性信息
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("角色信息：", EditorStyles.boldLabel);
            
            var testScript = importer.GetComponent<TestScruot>();
            if (testScript == null)
            {
                testScript = FindObjectOfType<TestScruot>();
            }
            
            if (testScript != null)
            {
                string currentCharacter = testScript.GetCurrentCharacterId();
                EditorGUILayout.LabelField($"当前场景角色：{(string.IsNullOrEmpty(currentCharacter) ? "无" : currentCharacter)}");
                
                // 获取需要的角色ID
                var loadedDataField = typeof(AudioClipImporterWithCustomJson).GetField("loadedData", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (loadedDataField != null)
                {
                    var loadedData = loadedDataField.GetValue(importer) as AudioExportData;
                    if (loadedData != null)
                    {
                        bool isMatch = currentCharacter == loadedData.avatarId;
                        EditorGUILayout.LabelField($"需要角色：{loadedData.avatarId}");
                        
                        string statusText = isMatch ? "✓ 角色匹配" : "✗ 角色不匹配";
                        EditorGUILayout.LabelField($"状态：{statusText}");
                        
                        if (!isMatch && dataLoaded.boolValue)
                        {
                            EditorGUILayout.HelpBox("角色不匹配！请重新加载数据。", MessageType.Warning);
                        }
                    }
                    else if (dataLoaded.boolValue)
                    {
                        EditorGUILayout.LabelField("需要角色：数据加载失败");
                    }
                }
            }
            else
            {
                EditorGUILayout.LabelField("TestScruot：未找到");
            }
            
            EditorGUILayout.Space();
            
            // JSON状态显示
            EditorGUILayout.LabelField("JSON状态：", EditorStyles.boldLabel);
            var customJsonDataProperty = serializedObject.FindProperty("customJsonData");
            bool hasJsonContent = !string.IsNullOrEmpty(customJsonDataProperty.stringValue);
            EditorGUILayout.LabelField($"JSON内容：{(hasJsonContent ? "已输入" : "未输入")}");
            
            if (hasJsonContent && !jsonParsed.boolValue)
            {
                EditorGUILayout.HelpBox("JSON已输入但未解析，请点击\"解析自定义JSON\"或\"加载音频数据和文件\"", MessageType.Info);
            }
            
            EditorGUILayout.Space();
            
            // 操作按钮
            EditorGUILayout.LabelField("操作：", EditorStyles.boldLabel);
            
            // 解析JSON按钮
            GUI.enabled = hasJsonContent;
            if (GUILayout.Button("解析自定义JSON"))
            {
                importer.ParseCustomJson();
            }
            GUI.enabled = true;
            
            if (GUILayout.Button("加载音频数据和文件", GUILayout.Height(30)))
            {
                importer.LoadAudioData();
            }
            
            EditorGUILayout.Space();
            
            GUI.enabled = dataLoaded.boolValue && audioLoaded.boolValue && characterReady.boolValue && jsonParsed.boolValue;
            if (GUILayout.Button("播放加载的数据", GUILayout.Height(30)))
            {
                importer.PlayLoadedData();
            }
            GUI.enabled = true;
            
            if (GUILayout.Button("停止播放"))
            {
                importer.StopPlayback();
            }
            
            EditorGUILayout.Space();
            
            // 信息按钮
            if (GUILayout.Button("显示状态信息"))
            {
                importer.LogStatusInfo();
            }
            
            EditorGUILayout.Space();
            EditorGUILayout.HelpBox("使用步骤：\n1. 在\"自定义JSON数据\"中输入对话JSON\n2. 设置导入文件名（不含扩展名）\n3. 点击\"加载音频数据和文件\"（会自动解析JSON并加载角色）\n4. 等待角色加载完成\n5. 点击\"播放加载的数据\"（使用自定义JSON而不是原始dialogData）", MessageType.Info);
        }
    }
}
#endif 