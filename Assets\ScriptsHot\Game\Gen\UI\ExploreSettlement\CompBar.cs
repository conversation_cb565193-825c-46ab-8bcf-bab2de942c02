/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreSettlement
{
    public partial class CompBar : UIBindT
    {
        public override string pkgName => "ExploreSettlement";
        public override string comName => "CompBar";

        public Controller state;
        public GProgressBar barAcq;
        public GProgressBar barFam;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);
            barAcq = (GProgressBar)com.GetChildAt(0);
            barFam = (GProgressBar)com.GetChildAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
            barAcq = null;
            barFam = null;
        }
    }
}